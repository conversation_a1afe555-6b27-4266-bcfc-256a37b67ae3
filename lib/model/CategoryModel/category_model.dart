import 'dart:convert';

import 'package:Rapsap/model/CategoryModel/data.dart';
import 'package:Rapsap/model/CategoryModel/meta.dart';

CategoryModel categoryModelFromJson(String str) =>
    CategoryModel.fromJson(json.decode(str));

String categoryModelToJson(CategoryModel data) => json.encode(data.toJson());

class CategoryModel {
  CategoryModel({
    this.success,
    this.msg,
    this.data,
    this.meta,
  });

  bool? success;
  String? msg;
  List<CategoryItemModel>? data;
  Meta? meta;

  factory CategoryModel.fromJson(Map<String, dynamic> json) => CategoryModel(
        success: json["success"],
        msg: json["msg"],
        data: List<CategoryItemModel>.from(
            json["data"].map((x) => CategoryItemModel.fromJson(x))),
        meta: Meta.fromJson(json["meta"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "msg": msg,
        "data": List<dynamic>.from(data!.map((x) => x.toJson())),
        "meta": meta?.toJson(),
      };
}
