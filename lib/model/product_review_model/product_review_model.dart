// To parse this JSON data, do
//
//     final productReviewModel = productReviewModelFromJson(jsonString);

import 'dart:convert';

import 'package:Rapsap/model/product_review_model/review_data_model.dart';

ProductReviewModel productReviewModelFromJson(String str) =>
    ProductReviewModel.fromJson(json.decode(str));

String productReviewModelToJson(ProductReviewModel data) =>
    json.encode(data.toJson());

class ProductReviewModel {
  ProductReviewModel({
    this.success,
    this.msg,
    this.data,
    this.meta,
  });

  bool? success;
  String? msg;
  List<ReviewDataModel>? data;
  Meta? meta;

  factory ProductReviewModel.fromJson(Map<String, dynamic> json) =>
      ProductReviewModel(
        success: json["success"],
        msg: json["msg"],
        data: List<ReviewDataModel>.from(
            json["data"].map((x) => ReviewDataModel.fromJson(x))),
        meta: Meta.fromJson(json["meta"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "msg": msg,
        "data": List<dynamic>.from(data!.map((x) => x.toJson())),
        "meta": meta!.toJson(),
      };
}

class Meta {
  Meta({
    this.total,
  });

  int? total;

  factory Meta.fromJson(Map<String, dynamic> json) => Meta(
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
        "total": total,
      };
}
