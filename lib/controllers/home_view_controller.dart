import 'package:Rapsap/model/homepageproducts/home_page_products/datum.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Rapsap/model/BannerAds/datum.dart';
import 'package:Rapsap/model/CategoryModel/data.dart';
import 'package:Rapsap/services/category_service.dart';

import '../services/orderservices.dart';
import '../view/screens/cart_screen/cart_screen.dart';

class HomeViewController extends GetxController {
  int _pageIndex = 0;
  RxBool favourite = false.obs;
  bool pinned = false;
  RxBool rewards = false.obs;

  bool _isCategoryLoading = false;
  RxInt selectedindex = 0.obs;
  RxString selectedfitler = "".obs;
  List<BannerItem> bannerItems = [];
  int selectedpageitem = 0;

  List<HomePageCategoryData> homepageDatalist = [];

  // List<CategoryProducts>? groceryproductmodel = [];
  // List<CategoryProducts>? seafoodmodel = [];
  // List<CategoryProducts>? jfmodel = [];
  // List<CategoryProducts>? fruitsmodel = [];

  List<CategoryItemModel?> _categoryModel = [];

  @override
  void onInit() async {
    await getHomePageViews();
    super.onInit();
  }

  getHomePageViews() async {
    getBannerAds();

    await getCategory();

    getHomePageData();
    var res = await OrderServices.getConfig();
    if (res != null) {
      configModel = res;
    }
  }

  List<CategoryItemModel?> get categoryModel => _categoryModel;
  set categoryModel(List<CategoryItemModel?> val) {
    _categoryModel = val;
    update(['home']);
  }

  bool get isCategoryLoading => _isCategoryLoading;
  set isCategoryLoading(bool val) {
    _isCategoryLoading = val;
    update();
  }

  final Widget bannerimage = Image.asset(
    "assets/images/banner-image.png",
    fit: BoxFit.cover,
    height: 180,
    width: double.infinity,
  );

  int get pageIndex => _pageIndex;
  set pageIndex(int val) {
    _pageIndex = val;
    update();
  }

  Future<List<CategoryItemModel?>> getCategory() async {
    // isCategoryLoading = true;
    final category = await CategoryService.getCategoryList();
    if (category != null) {
      categoryModel = category.data!;
      // isCategoryLoading = false;

      update(['home']);
    }
    checkDataLoaded();

    return categoryModel;
  }

  Future<List<BannerItem?>> getBannerAds() async {
    final res = await CategoryService.getBannerAds();
    // ignore: prefer_typing_uninitialized_variables
    final list;
    print("banner=$res");
    if (res != null) {
      bannerItems = res.data!;
    }
    list = res?.data;
    update(['home']);
    checkDataLoaded();

    return list;
  }

  // Future<List<CategoryProducts?>> getgroceries() async {
  //   final res = await CategoryService.getProductsByCategory(
  //     type: "home",
  //     categoryId: categoryModel
  //         .where((element) => element!.name!.contains('Groceries'))
  //         .first!
  //         .categoryId!,
  //   );
  //   if (res != null) {
  //     groceryproductmodel = res.data;
  //     update();
  //   }

  //   // print(categoryproductmodel.toList());
  //   return categoryproductmodel.toList();
  // }

  // Future<List<CategoryProducts?>> getSeafood() async {
  //   final res = await CategoryService.getProductsByCategory(
  //     categoryId: categoryModel
  //         .where((element) => element!.name!.contains('Chicken'))
  //         .first!
  //         .categoryId!,
  //   );
  //   if (res != null) {
  //     seafoodmodel = res.data;
  //     update();
  //   }

  //   // print(categoryproductmodel.toList());
  //   return categoryproductmodel.toList();
  // }

  // Future<List<CategoryProducts?>> getfruitsVegetables() async {
  //   final res = await CategoryService.getProductsByCategory(
  //     categoryId: categoryModel
  //         .where((element) => element!.name!.contains('Fruits'))
  //         .first!
  //         .categoryId!,
  //   );
  //   if (res != null) {
  //     fruitsmodel = res.data;
  //     update();
  //   }

  //   // print(categoryproductmodel.toList());
  //   return categoryproductmodel.toList();
  // }

  // Future<List<CategoryProducts?>> getcategoryrewards() async {
  //   final res = await CategoryService.getProductsByCategory(
  //     categoryId: categoryModel
  //         .where((element) => element!.name!.contains('JF-REWARDS'))
  //         .first!
  //         .categoryId!,
  //   );
  //   if (res != null) {
  //     jfmodel = res.data;
  //     update();
  //   }

  //   // print(categoryproductmodel.toList());
  //   return categoryproductmodel.toList();
  // }

  checkDataLoaded() {
    if (categoryModel.isNotEmpty &&
        categoryModel.first != null &&
        homepageDatalist.isNotEmpty &&
        bannerItems.isNotEmpty) {
      update(['home']);
    }
  }

  Future<List<HomePageCategoryData?>> getHomePageData() async {
    final res = await CategoryService.getHomePageProductsByCategory();
    if (res != null) {
      homepageDatalist = res.data!;

      update(['home']);
    }
    checkDataLoaded();

    print(homepageDatalist.toList());
    return homepageDatalist.toList();
  }
}
