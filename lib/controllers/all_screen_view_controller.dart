import 'package:get/get.dart';
import 'package:Rapsap/model/product_review_model/review_data_model.dart';
import 'package:Rapsap/services/product_service.dart';

class AllReviewViewController extends GetxController {
  List<ReviewDataModel>? _allReviewList = [];

  bool _isReviewLoading = false;
  int selectedindex = 0;

  List<ReviewDataModel>? get allReviewList => _allReviewList;
  set allReviewList(List<ReviewDataModel>? val) {
    _allReviewList = val;
    update();
  }

  bool get isReviewLoading => _isReviewLoading;
  set isReviewLoading(bool val) {
    _isReviewLoading = val;
    update();
  }

  Future<List<ReviewDataModel>?> getAllReviewsById(
      int productId, int rating) async {
    isReviewLoading = true;
    update();
    final res = await ProductService.getProductReviews(
        productId: productId, rating: rating);
    allReviewList = res!.data;
    // await Future.delayed(const Duration(seconds: 3));
    isReviewLoading = false;
    update();
    return allReviewList;
  }
}
