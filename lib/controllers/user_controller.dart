import 'dart:developer';

import 'package:Rapsap/view/screens/AddressPage/addaddresspage.dart';
import 'package:carousel_slider/carousel_controller.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:get/get.dart';
import 'package:mobile_number_picker/mobile_number_picker.dart';
import 'package:Rapsap/model/user_model/usermodel.dart';
import 'package:Rapsap/view/screens/mapscreen/mappage.dart';
import 'package:Rapsap/view/screens/root_page/root_page.dart';

import '../main.dart';
import '../services/firebaseservices.dart';
import '../services/userservices.dart';
import '../view/screens/login/personaldetailscreen.dart';

class UserController extends GetxController {
  RxBool showpassword = true.obs;
  String error = "";
  RxBool serviceloading = false.obs;
  RxBool tester = false.obs;

  String currentpin = "";
  bool loading = false;
  RxBool buttonloading = false.obs;

  RxBool numberSelected = true.obs;
  MobileNumberPicker mobileNumber = MobileNumberPicker();
  MobileNumber mobileNumberObject = MobileNumber();
  final Rx<TextEditingController> phoneController = TextEditingController().obs;
  Rx<UserData> userdata = const UserData().obs;
  RxBool loginstatus = false.obs;
  var locationaddress = Placemark().obs;
  var locationpoint = {}.obs;
  RxString image = ''.obs;

//
  // Mobileno Get
//

  void getmobilenumber(CarouselController controller, FocusNode otpctrlr) {
    mobileNumber.mobileNumber();
    mobileNumber.getMobileNumberStream.listen((MobileNumber? event) {
      if (event!.states == PhoneNumberStates.PhoneNumberSelected) {
        mobileNumberObject = event;
        if (kDebugMode) {
          print(mobileNumberObject.phoneNumber!);
        }

        phoneController.value.text = mobileNumberObject.phoneNumber!;
        FocusManager.instance.primaryFocus!.unfocus();

        if (phoneController.value.text.trim().isNotEmpty ||
            phoneController.value.text.length == 10) {
          update();
          sendOtp({"mobile": phoneController.value.text});

          controller.animateToPage(1);
          otpctrlr.requestFocus();
        } else {
          // Get.closeAllSnackbars();
          // Get.snackbar('Invalid Phone no', 'Enter valid phone no',
          //     backgroundColor: Colors.red[200]);
        }
      }
      if (event.states == PhoneNumberStates.NoneOfTheAbove) {
        numberSelected.value = false;
      }
    });
  }

  //
  //Register User
  //

  void registerUser(params) async {
    print("xjsxnjxjx $params");
    var user = await UserService.registerUsr(params);
    if (kDebugMode) {
      print('user $user');
    }
    if (user.success == true) {
      await storage.write('JWT', user.data?.token);
      log(storage.read('JWT'));
      // currentUser.value = _user;
      // print('current user ${currentUser.value.msg}');
      storage.write('userdata', UserData(data: user.data).toJson());
      userdata.value = UserData(data: user.data);
      loginstatus.value = true;
      storage.write('loginstatus', loginstatus.value);
      String? token = await FirebaseMessaging.instance.getToken();
      var req = {
        'user_id': userdata.value.data!.id.toString(),
        'user_firebase_token': token,
      };
      await UserService.saveDeviceToken(req);
      await FirebaseService.firebaseAnalytics.logEvent(
        name: 'login_user',
        parameters: {
          'name': userdata.value.data!.firstName,
          'userid': userdata.value.data!.id,
        },
      );
      await FirebaseService.firebaseAnalytics.logLogin(loginMethod: "email");

      if (storage.read('locationaddress') != null) {
        locationaddress.value =
            Placemark.fromMap(storage.read('locationaddress'));
      }
      if (userdata.value.data?.deleted == 1) {
        Future.delayed(
            const Duration(milliseconds: 1000),
            (() => Get.to(() => const RootPage(
                  data: true,
                ))));
        return;
      }

      Future.delayed(const Duration(milliseconds: 200), () {
        Get.to(() =>
            storage.read('tempPincode') == null ? MapPage() : const RootPage());
      });
      // updateFirebaseToken(_user.data!.id);
    } else {
      Get.back();

      customToast(message: 'Signup details invalid! try again.');
    }
  }

  void editProfile(params) async {
    buttonloading.value = true;
    print("xjsxnjxjx $params");
    var user = await UserService.updateProfile(params);
    if (kDebugMode) {
      print('user $user');
    }
    if (user['success'] == true) {
      // await storage.write('JWT', _user.data?.token);
      // log(storage.read('JWT'));
      // // currentUser.value = _user;
      // // print('current user ${currentUser.value.msg}');
      // storage.write('userdata', UserData(data: _user.data).toJson());
      // userdata.value = UserData(data: _user.data);
      loginstatus.value = true;
      storage.write('loginstatus', loginstatus.value);
      String? token = await FirebaseMessaging.instance.getToken();
      var req = {
        'user_id': userdata.value.data!.id.toString(),
        'user_firebase_token': token,
      };
      await UserService.saveDeviceToken(req);

      if (storage.read('locationaddress') != null) {
        locationaddress.value =
            Placemark.fromMap(storage.read('locationaddress'));
      }
      var userdtan = await UserService.getUserDetails({
        'user_id': userdata.value.data!.id.toString(),
      });
      if (userdtan?.data != null) {
        userdata.value = userdtan!;
        debugPrint(userdtan.toString());
      } else {
        buttonloading.value = false;
      }

      storage.write('userdata', UserData(data: userdtan?.data).toJson());

      if (userdata.value.data?.deleted == 1) {
        Future.delayed(
            const Duration(milliseconds: 500),
            (() => Get.to(() => const RootPage(
                  data: true,
                ))));
        buttonloading.value = false;

        return;
      }

      Future.delayed(const Duration(milliseconds: 100), () {
        Get.to(() =>
            storage.read('tempPincode') == null ? MapPage() : const RootPage());
        buttonloading.value = false;
      });

      // updateFirebaseToken(_user.data!.id);
    } else {
      buttonloading.value = false;

      if (user['msg'] != null && user['msg'] != "") {
        customToast(message: user['msg']);
      } else {
        customToast(message: 'Signup details invalid! try again.');
      }
    }
  }

  void fetchUserWithEmail(params) async {
    var user = await UserService.emailLogin(params);
    if (kDebugMode) {
      print('user $user');
    }
    if (user != null) {
      if (user.success == true) {
        await storage.write('JWT', user.data?.token);
        log(storage.read('JWT'));
        // currentUser.value = _user;
        // print('current user ${currentUser.value.msg}');
        storage.write('userdata', UserData(data: user.data).toJson());

        userdata.value = UserData(data: user.data);
        loginstatus.value = true;
        storage.write('loginstatus', loginstatus.value);
        String? token = await FirebaseMessaging.instance.getToken();
        var req = {
          'user_id': userdata.value.data!.id.toString(),
          'user_firebase_token': token,
        };
        await UserService.saveDeviceToken(req);

        if (storage.read('locationaddress') != null) {
          locationaddress.value =
              Placemark.fromMap(storage.read('locationaddress'));
        }
        await FirebaseService.firebaseAnalytics.logEvent(
          name: 'login_user',
          parameters: {
            'name': userdata.value.data!.firstName,
            'userid': userdata.value.data!.id,
          },
        );
        await FirebaseService.firebaseAnalytics.logLogin(loginMethod: "email");
        if (userdata.value.data?.deleted == 1) {
          Future.delayed(
              const Duration(milliseconds: 1000),
              (() => Get.to(() => const RootPage(
                    data: true,
                  ))));
          return;
        }

        Future.delayed(const Duration(seconds: 1), () {
          Get.to(() => storage.read('tempPincode') == null
              ? MapPage()
              : const RootPage());
        });
        // updateFirebaseToken(_user.data!.id);
      } else {
        Get.back();

        customToast(message: 'Login details invalid! try again.');
      }
    } else {
      Get.back();
      customToast(message: 'Error');
    }
  }

  void googleSigninVerify(params) async {
    var user = await UserService.onGoogleSignin(params);
    if (kDebugMode) {
      print('user $user');
    }
    if (user.success == true) {
      await storage.write('JWT', user.data?.token);
      log(storage.read('JWT'));
      // currentUser.value = _user;
      // print('current user ${currentUser.value.msg}');
      storage.write('userdata', UserData(data: user.data).toJson());

      userdata.value = UserData(data: user.data);
      loginstatus.value = true;
      storage.write('loginstatus', loginstatus.value);
      String? token = await FirebaseMessaging.instance.getToken();
      var req = {
        'user_id': userdata.value.data!.id.toString(),
        'user_firebase_token': token,
      };
      await UserService.saveDeviceToken(req);

      if (storage.read('locationaddress') != null) {
        locationaddress.value =
            Placemark.fromMap(storage.read('locationaddress'));
      }
      await FirebaseService.firebaseAnalytics.logEvent(
        name: 'login_user',
        parameters: {
          'name': userdata.value.data!.firstName,
          'userid': userdata.value.data!.id,
        },
      );
      await FirebaseService.firebaseAnalytics.logLogin(loginMethod: "google");
      if (userdata.value.data?.deleted == 1) {
        Future.delayed(
            const Duration(milliseconds: 1000),
            (() => Get.to(() => const RootPage(
                  data: true,
                ))));
        return;
      }

      Future.delayed(const Duration(milliseconds: 10), () {
        Get.to(() =>
            storage.read('tempPincode') == null ? MapPage() : const RootPage());
      });
      // updateFirebaseToken(_user.data!.id);
    } else {
      Get.back();

      customToast(message: 'Login details invalid! try again.');
    }
  }

  void verifyOtp(params) async {
    // error = "";

    loading = true;
    update();

    var user = await UserService.verifyOtp(params);
    if (kDebugMode) {
      print('user $user');
    }
    if (user != null) {
      if (user.success == true) {
        error = "";
        update();
        await storage.write('JWT', user.data?.token);
        log(storage.read('JWT'));

        storage.write('userdata', UserData(data: user.data).toJson());
        userdata.value = UserData(data: user.data);

        if (user.data?.is_newuser == 1) {
          Future.delayed(const Duration(milliseconds: 10), () {
            Get.offAll(() => PersonalDetailsScreen(
                  userid: user.data?.id,
                  phonenumber: params['mobile'],
                ));
          });
        } else {
          storage.write('userdata', UserData(data: user.data).toJson());
          userdata.value = UserData(data: user.data);
          loginstatus.value = true;
          storage.write('loginstatus', loginstatus.value);
          String? token = await FirebaseMessaging.instance.getToken();
          var req = {
            'user_id': userdata.value.data!.id.toString(),
            'user_firebase_token': token,
          };
          await UserService.saveDeviceToken(req);
          await FirebaseService.firebaseAnalytics
              .setUserId(id: userdata.value.data!.id.toString())
              .then((value) => print('done'));

          UserService.getUserConfig("splash");
          if (storage.read('locationaddress') != null) {
            locationaddress.value =
                Placemark.fromMap(storage.read('locationaddress'));
          }

          if (userdata.value.data?.deleted == 1) {
            Future.delayed(
                const Duration(milliseconds: 100),
                (() => Get.to(() => const RootPage(
                      data: true,
                    ))));
            loading = false;
            update();

            return;
          }
          Future.delayed(const Duration(milliseconds: 100), () {
            Get.offAll(() => storage.read('tempPincode') == null
                ? MapPage()
                : const RootPage());
            loading = false;
            update();
          });
          await FirebaseService.firebaseAnalytics.logEvent(
            name: 'login_user',
            parameters: {
              'name': userdata.value.data!.firstName,
              'userid': userdata.value.data!.id,
            },
          );

          await FirebaseService.firebaseAnalytics
              .logLogin(loginMethod: "phone");
          // }
          // updateFirebaseToken(_user.data!.id);
        }
      } else {
        print('error');
        loading = false;
        update();

        // Get.back();
        error = user.msg;
        update();

        // customSnackBar(success: false, title: 'Error', message: _user.msg!);
      }
    } else {
      print('error');
      loading = false;
      update();

      // Get.back();

      // customSnackBar(success: false, title: 'Error', message: _user.msg!);
    }
  }

  void sendOtp(params) async {
    // Get.to(() => OtpVerificationScreen(
    //       phoneno: params['mobile'],
    //     ));
    var user = await UserService.sendOTP(params);

    if (kDebugMode) {
      print('user $user');
    }
    if (user.success == true) {
      currentpin = user.data.otp;

      // currentUser.value = _user;
      // print('current user ${currentUser.value.msg}');

      // updateFirebaseToken(_user.data!.id);
    } else {
      customToast(message: 'Login details invalid! try again.');
    }
  }

  @override
  void onInit() {
    loginstatus.value = false;
    // TODO: implement onInit
    super.onInit();
  }
}


// customSnackBar(
//     {String title = '',
//     String message = '',
//     bool success = true,
//     Duration duration = const Duration(seconds: 2)}) {
//   return Get.snackbar(
//     title,
//     message,

//     icon: Icon(
//       success ? Icons.check_circle : Icons.close,
//       size: 25,
//       color: Colors.white,
//     ),
//     backgroundColor: success ? kblack : Colors.redAccent,
//     duration: duration,
//     isDismissible: true,
//     colorText: kwhite,
//     snackStyle: SnackStyle.GROUNDED,
//     snackPosition: SnackPosition.TOP,
//     boxShadows: [
//       const BoxShadow(
//         blurRadius: 2,
//         color: Colors.black12,
//         offset: Offset(1, 2),
//         spreadRadius: 0.1,
//       )
//     ],
//     // borderWidth: 2,
//   );
// }
