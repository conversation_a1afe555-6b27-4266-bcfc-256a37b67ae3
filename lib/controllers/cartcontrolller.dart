import 'package:flutter/foundation.dart';
import 'package:Rapsap/services/databaseHelper.dart';
import 'package:Rapsap/view/screens/cart_screen/cart_screen.dart';
import 'package:Rapsap/view/widgets/commons.dart';

class CartController extends GetxController {
  GetStorage cartstorage = GetStorage('cart');
  RxList<ShopingCart> myCartItems = <ShopingCart>[].obs;
  var totalitems = 0.obs;
  RxBool loading = true.obs;
  Rx<double> totalprice = 0.00.obs;
  bool rebuild = false;
  String coupontext = "";
  int apllied = 1000;
  bool buttonloading = false;
  bool orderbuttonloading = false;
  double height = 0;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
  }
}
