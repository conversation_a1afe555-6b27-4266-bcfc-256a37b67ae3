import 'package:get/get.dart';
import 'package:Rapsap/model/productmodel/products/products.dart';
import 'package:Rapsap/model/product_review_model/product_review_model.dart';
import 'package:Rapsap/services/product_service.dart';

class ProductViewController extends GetxController {
  int _carouselIndex = 0;
  RxInt variant_index = 0.obs;
  bool favourite = false;
  double currentdiscount = 0;

  List<ProductReviewModel> reviewList = [];

  bool _productDetailsTileExpanded = false;

  int get carouselIndex => _carouselIndex;
  set carouselIndex(int val) {
    _carouselIndex = val;
    update();
  }

  bool get productDetailsTileExpanded => _productDetailsTileExpanded;
  set productDetailsTileExpanded(bool val) {
    _productDetailsTileExpanded = val;
    update();
  }

  Future<ProductReviewModel?> getProductReviews(
      int productId, int rating) async {
    print("function called ");
    final result = await ProductService.getProductReviews(
      productId: productId,
      rating: rating,
    );
    return result;
  }

  Future<Products?> getproducts(int productId) async {
    print("function called ");
    final result = await ProductService.getProductbyID(
      productId: productId,
    );

    print("res=$result");

    return result;
  }

  int getProductImagesCount(int productImageLength) {
    update();
    return productImageLength;
  }
}
