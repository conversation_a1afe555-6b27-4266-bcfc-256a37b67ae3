import 'dart:io';
import 'package:Rapsap/services/firebaseservices.dart';
import 'package:Rapsap/view/widgets/Internetconnectiviity.dart';
import 'package:Rapsap/view/widgets/keyboardhider.dart';
import 'package:Rapsap/view/widgets/notification_service.dart';
import 'package:flutter/services.dart';
import 'package:flutter_displaymode/flutter_displaymode.dart';
import 'view/widgets/commons.dart';
// ignore: unused_import
import 'package:firebase_performance/firebase_performance.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorage.init('payload');
  if (Platform.isAndroid) {
    await FlutterDisplayMode.setHighRefreshRate();
  }
  await FirebaseService.init();
  HomeBinding().dependencies();
  // InternetConnectivity().initConnectivity();
  await GetStorage.init();
  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp])
      .then((_) {
    runApp(const Rapsap());
  });
}

GetStorage storage = GetStorage();

class Rapsap extends StatelessWidget {
  const Rapsap({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return KeyboardHider(
      child: GetMaterialApp(
        onInit: () => NotificationService().setupInteractedMessage(),
        navigatorObservers: FirebaseService.analyticsObserver,
        defaultTransition: Transition.rightToLeft,
        initialBinding: HomeBinding(),
        theme: RapsapTheme.theme,
        debugShowCheckedModeBanner: false,
        home: const SplashScreen(),
      ),
    );
  }
}
