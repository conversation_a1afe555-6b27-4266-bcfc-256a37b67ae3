import 'package:Rapsap/controllers/categorycontroller.dart';
import 'package:Rapsap/controllers/wishlistcontroller.dart';
import 'package:get/get.dart';
import 'package:Rapsap/controllers/accountscontroller.dart';
import 'package:Rapsap/controllers/cartcontrolller.dart';
import 'package:Rapsap/controllers/home_view_controller.dart';
import 'package:Rapsap/controllers/ordercontroller.dart';
import 'package:Rapsap/controllers/product_view_controller.dart';
import 'package:Rapsap/controllers/ratingscreencontroller.dart';
import 'package:Rapsap/controllers/user_controller.dart';

class HomeBinding implements Bindings {
  @override
  void dependencies() {
    Get.put<UserController>(UserController());
    Get.put<HomeViewController>(HomeViewController());
    Get.put<CartController>(CartController());
    Get.put<ProductViewController>(ProductViewController());
    Get.put<OrderController>(OrderController());
    Get.lazyPut<AccountController>(() => AccountController());
    Get.put<RatingScrnController>(RatingScrnController());
    Get.put<Wishlistcontroller>(Wishlistcontroller());
    Get.put<CategoryController>(CategoryController());
  }
}
