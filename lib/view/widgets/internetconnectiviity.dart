import 'dart:async';
import 'dart:developer';

import 'package:Rapsap/view/widgets/commons.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/services.dart';
import 'package:rive/rive.dart';

class InternetConnectivity {
  ConnectivityResult connectionStatus = ConnectivityResult.none;
  final Connectivity connectivity = Connectivity();
  late StreamSubscription<ConnectivityResult> connectivitySubscription;

  Future<void> initConnectivity() async {
    late ConnectivityResult result;
    // Platform messages may fail, so we use a try/catch PlatformException.
    try {
      result = await connectivity.checkConnectivity();
    } on PlatformException catch (e) {
      log('Couldn\'t check connectivity status', error: e);
      return;
    }

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    // if (!mounted) {
    //   return Future.value(null);
    // }

    return updateConnectionStatus(result);
  }

  Future<void> updateConnectionStatus(ConnectivityResult result) async {
    print(result);
    connectionStatus = result;
    if (connectionStatus == ConnectivityResult.none) {
      InternetCheckStatus.showInternetConnectionStatus();
    } else {
      InternetCheckStatus.hideInternetStatusDialog();
    }
  }
}

class InternetCheckStatus {
  static void showInternetConnectionStatus() {
    Get.isSnackbarOpen ? Get.closeAllSnackbars() : null;
    Future.delayed(const Duration(milliseconds: 0)).then((_) {
      Get.dialog(
        Dialog(
          elevation: 0,
          backgroundColor: Colors.white,
          alignment: Alignment.center,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: ClipRRect(
              child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
            ),
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(
                    height: 70,
                    child: RiveAnimation.asset('assets/rive/no_internet.riv')),
                kheight20,
                const Text(' Oops!  No internet',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: kblack,
                    )),
                const SizedBox(height: 20),
                Text(
                    'Make sure your Wi-Fi or mobile data is turned on, then try again,',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                      color: kblack.withOpacity(0.5),
                    )),
                const SizedBox(height: 10),
              ],
            ),
          )),
        ),
        barrierDismissible: false,
        barrierColor: Colors.black.withOpacity(0.5),
      );
    });
  }

  static void hideInternetStatusDialog() {
    if (Get.isDialogOpen == true) {
      Get.back();
    }
  }
}
