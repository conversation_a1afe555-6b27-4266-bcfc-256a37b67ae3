import 'package:Rapsap/controllers/home_view_controller.dart';
import 'package:Rapsap/controllers/root_view_controller.dart';
import 'package:Rapsap/view/screens/AddressPage/addaddresspage.dart';
import 'package:Rapsap/view/widgets/custom.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:confetti/confetti.dart';
import 'package:flutter/services.dart';

import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';

import 'package:intl/intl.dart';
import 'package:Rapsap/model/rewards/rewardsmode.dart';
import 'package:Rapsap/services/accountservices.dart';
import 'package:Rapsap/services/userservices.dart';
import 'package:Rapsap/view/screens/login/widgets/button.dart';
import 'package:Rapsap/view/screens/root_page/root_page.dart';
import 'package:Rapsap/view/widgets/commons.dart';
import 'package:scratcher/scratcher.dart';
import 'package:shimmer/shimmer.dart';

class MyRewardsScreen extends StatefulWidget {
  MyRewardsScreen({Key? key}) : super(key: key);

  @override
  State<MyRewardsScreen> createState() => _MyRewardsScreenState();
}

class _MyRewardsScreenState extends State<MyRewardsScreen> {
  late ConfettiController _controller;
  final scratchKey = GlobalKey<ScratcherState>();
  bool loading = true;

  @override
  void initState() {
    super.initState();
    _controller = ConfettiController(
      duration: Duration(seconds: 1),
    );

    final controller = Get.find<HomeViewController>();

    AccountService.checkDailyRewards().then((value) {
      if (value['success'] == true) {
        var data = value['data'];
        controller.rewards.value = true;
        scratchDialog(context, data);
      } else {
        controller.rewards.value = false;
      }
    });

    AccountService.getUserRewards1().then((value) {
      setState(() {
        rewards = value;
        loading = false;
      });
    });
  }

  late List<Rewards> rewards = [];

  List<String> imagePaths = [];

  double _opacity = 0.0;

  double _scratchProgress = 0.0;

  bool thresholdReached = false;

  @override
  Widget build(BuildContext context) {
    // Future<List<Early>> _rewards;
    // final rewardController = Get.put(RewardController());

    // final List<Map> myProducts =
    //     List.generate(10, (index) => {"id": index, "name": "Product $index"})
    //         .toList();

    return SafeArea(
        child: Scaffold(
      appBar: PreferredSize(
          preferredSize: const Size.fromHeight(149),
          child: Container(
            color: kblack,
            child: Stack(
              children: [
                SvgPicture.asset("assets/svg/headergiftbg.svg"),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Flexible(
                      flex: 4,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          kheight10,
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              InkWell(
                                onTap: () => Get.back(),
                                child: Icon(
                                  Icons.arrow_back,
                                  color: kwhite,
                                ),
                              ),
                            ],
                          ),
                          kheight30,
                          Row(
                            children: [
                              Text(
                                "My Rewards",
                                style: TextStyle(
                                    color: kwhite,
                                    fontSize: 24,
                                    fontWeight: FontWeight.w700),
                              ),
                            ],
                          ),
                        ],
                      ).paddingSymmetric(horizontal: 24),
                    ),
                    Flexible(
                        flex: 3,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            SvgPicture.asset("assets/svg/rewardgift.svg"),
                          ],
                        )),
                  ],
                ),
              ],
            ),
          )),
      body: Padding(
        padding: const EdgeInsets.all(14.0),
        child: loading
            ? GridView.builder(
                gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
                    maxCrossAxisExtent: 200,
                    childAspectRatio: 3 / 4,
                    crossAxisSpacing: 15,
                    mainAxisSpacing: 15),
                itemCount: 7,
                itemBuilder: (BuildContext ctx, index) {
                  var count = index + 1;
                  return Container(
                      alignment: Alignment.center,
                      child: Shimmer.fromColors(
                          baseColor: Colors.grey[200]!,
                          highlightColor: Colors.grey[100]!,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(2),
                            child: Container(
                              width: double.infinity,
                              color: Colors.grey,
                            ),
                          )));
                })
            : rewards.isEmpty
                ? SizedBox(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        hgap(150),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text('No Rewards'),
                          ],
                        ),
                      ],
                    ),
                  )
                : GridView.builder(
                    gridDelegate:
                        const SliverGridDelegateWithMaxCrossAxisExtent(
                            maxCrossAxisExtent: 200,
                            mainAxisExtent: 250,
                            crossAxisSpacing: 15,
                            mainAxisSpacing: 15),
                    itemCount: null == rewards ? 3 : rewards.length,
                    itemBuilder: (BuildContext ctx, index) {
                      // print(snapshot.data[index]);
                      if (rewards[index].isActive == 1) {
                        return InkWell(
                          onTap: () {
                            print('clicked? ${rewards[index].id}');
                            showBarModalBottomSheet(
                                context: context,
                                // expand: true,
                                bounce: true,
                                enableDrag: true,
                                backgroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  side: BorderSide(color: Colors.white),
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(20),
                                    topRight: Radius.circular(20),
                                  ),
                                ),
                                builder: (context) => SingleChildScrollView(
                                    child: RewardsDetailPage(
                                        rewards: rewards, index: index)));
                          },
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(2),
                            child: Container(
                              padding: const EdgeInsets.all(10),
                              alignment: Alignment.center,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.all(10.0),
                                    child: CachedNetworkImage(
                                        height: 110,
                                        width: 110,
                                        imageUrl: rewards[index].imageUrl ??
                                            'https://junq-bucket.s3.ap-south-1.amazonaws.com/common/coin.png',
                                        placeholder: ((context, url) {
                                          return Image.asset(
                                              'assets/images/error-image.png');
                                        })),
                                  ),
                                  Text(
                                    rewards[index].name.toString(),
                                    textAlign: TextAlign.center,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                        color: kblack,
                                        fontSize: 18,
                                        fontWeight: FontWeight.w600),
                                  ),
                                  Text(
                                    'Come back to unlock more rewards.',
                                    textAlign: TextAlign.center,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                        color: Colors.grey,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500),
                                  ),
                                ],
                              ),
                              decoration: BoxDecoration(
                                color: Color(0x4096E6F8),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey
                                        .withOpacity(0.1), //color of shadow
                                    spreadRadius: 1, //spread radius
                                    blurRadius: 1, // blur radius
                                    offset: Offset(
                                        0, 2), // changes position of shadow
                                    //first paramerter of offset is left-right
                                    //second parameter is top to down
                                  ),
                                  //you can set more BoxShadow() here
                                ],
                                image: DecorationImage(
                                    image: AssetImage(
                                        'assets/images/reward-bg.png'),
                                    fit: BoxFit.cover),
                              ),
                            ),
                          ),
                        );
                      } else {
                        var count = index + 1;
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(2),
                          child: Container(
                            alignment: Alignment.center,
                            padding: const EdgeInsets.all(20),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Column(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  children: [
                                    Text(
                                      'Reward $count',
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600),
                                    ),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: Text(
                                            'Come back to unlock more rewards.',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 14,
                                                fontWeight: FontWeight.w500),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            decoration: BoxDecoration(
                              color: kblack,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.4),
                                  spreadRadius: 1,
                                  blurRadius: 1,
                                  offset: Offset(
                                      0, 3), // changes position of shadow
                                ),
                              ],
                              image: DecorationImage(
                                  image: AssetImage("assets/images/unlock.png"),
                                  fit: BoxFit.cover),
                            ),
                          ),
                        );
                      }
                    }),
      ),
      // floatingActionButton: FloatingActionButton(onPressed: () {
      //   // scratchDialog(context);
      // }),
    ));
  }

  void _onDeleteImage(int position) {
    // setState(() {
    imagePaths.removeAt(position);
    // });
  }

  // void _onShare(BuildContext context) async {
  Future<void> scratchDialog(BuildContext context, var data) {
    return showDialog(
        context: context,
        barrierColor: Color(0xFFF3F4F9),
        useRootNavigator: false,
        barrierDismissible: false,

        // barrierLabel: 'Timepass',
        builder: (BuildContext context) {
          return AlertDialog(
            insetPadding: EdgeInsets.symmetric(horizontal: 30),
            iconPadding: EdgeInsets.symmetric(horizontal: 0, vertical: 50),
            alignment: Alignment.topLeft,
            // shape: RoundedRectangleBorder(
            //   borderRadius: BorderRadius.circular(0),
            // ),

            backgroundColor: Color(0xFFF3F4F9),
            elevation: 0,
            icon: Align(
              alignment: Alignment.topLeft,
              child: IconButton(
                  onPressed: () {
                    Get.close(1);
                  },
                  icon: const Icon(
                    Icons.close,
                    color: kblack,
                  )),
            ),
            title: Column(
              children: [
                Align(
                  alignment: Alignment.topCenter,
                  child: SizedBox(
                    child: SvgPicture.asset(
                      "assets/svg/rapsaptitle.svg",
                      color: kblack,
                      fit: BoxFit.fitHeight,
                    ),
                  ),
                ),
                Text('Early bird reward')
              ],
            ),

            actions: [
              SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Text(
                      'Hooray!',
                      style: TextStyle(
                          color: Color(0xFF18394C),
                          fontSize: 18,
                          fontWeight: FontWeight.w500),
                    ),
                    kheight10,
                    Text(
                      'You’ve won a scratch card. Scratch the above card earn a reward!!',
                      textAlign: TextAlign.center,
                    ),
                    kheight10,
                    Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                      ElevatedButton(
                          onPressed: () {
                            if (_scratchProgress > 50) {
                              print('scratch card opened');

                              var payload = {'offer_id': data['id']};
                              UserService.claimDailyRewards(payload)
                                  .then((value) {
                                print('claim rewards value ${value}');
                                if (value['success'] == true) {
                                  UserService.getUserRewards1().then((value) {
                                    setState(() {
                                      rewards = value;
                                      print(value[0].name);
                                    });
                                    Get.close(1);
                                    Get.to(() => MyRewardsScreen());
                                  });
                                }
                              });
                            } else {
                              customToast(
                                  message:
                                      'You need to scratch the card before continue!');
                            }
                          },
                          child: Text('Continue'),
                          style: ElevatedButton.styleFrom(
                              backgroundColor: kblack,
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(2))))
                    ]),
                  ],
                ),
              ),
            ],

            content: StatefulBuilder(builder: (context, StateSetter setState) {
              return ClipRRect(
                borderRadius: BorderRadius.circular(3),
                child: Scratcher(
                  // color: kblack,
                  key: scratchKey,

                  accuracy: ScratchAccuracy.low,
                  image: Image.asset('assets/images/scratchcard.png'),
                  color: Color(0xFFF3F4F9),
                  threshold: 50,
                  brushSize: 60,
                  onThreshold: () {
                    setState(() {
                      _opacity = 1;
                    });

                    // print('this._opacity ${this._opacity}');
                  },
                  onChange: (progress) {
                    // print('progress $progress');
                    setState(() {
                      _scratchProgress = progress;
                    });
                  },

                  onScratchEnd: () {
                    if (_scratchProgress > 50) {
                      scratchKey.currentState!
                          .reveal(duration: Duration(milliseconds: 500));
                      Future.delayed(
                          Duration(
                            milliseconds: 500,
                          ), (() {
                        _controller.play();
                      }));

                      print('Congratulations ');
                    }
                  },
                  child: AnimatedOpacity(
                    duration: Duration(milliseconds: 100),
                    opacity: _opacity,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(3),
                      child: Container(
                        // color: Colors.white,
                        height: 250,
                        width: 300,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.zero,
                          color: Colors.white,
                        ),
                        padding: const EdgeInsets.all(15),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Expanded(
                              child: Image.network(
                                null == data['image_url']
                                    ? 'https://prod-junq.s3.ap-south-1.amazonaws.com/icons8-party-popper-100.png'
                                    : data['image_url'],
                                fit: BoxFit.scaleDown,
                              ),
                            ),
                            Text(
                              'Yay!',
                              style: TextStyle(
                                  color: kblack,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600),
                            ),
                            Text(data['name'],
                                style: TextStyle(
                                    color: Color(0xFF18394C), fontSize: 18)),
                            Text(data['description'],
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    color: Colors.grey, fontSize: 12)),
                            ConfettiWidget(
                              blastDirectionality:
                                  BlastDirectionality.explosive,
                              confettiController: _controller,
                              particleDrag: 0.05,
                              emissionFrequency: 0.05,
                              numberOfParticles: 10,
                              gravity: 0.05,
                              shouldLoop: false,
                              colors: [
                                Colors.green,
                                kblack,
                                Colors.yellow,
                                kblue
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }),
          );
        });
  }
}

class RewardsDetailPage extends StatelessWidget {
  final int index;

  final rewards;

  const RewardsDetailPage(
      {Key? key, required this.rewards, required this.index})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(25),
      color: Colors.grey[100],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            child: SvgPicture.asset(
              "assets/svg/rapsaptitle.svg",
              color: kblack,
              fit: BoxFit.fitHeight,
            ),
          ),
          Text(
            'Early bird reward',
            style: TextStyle(
              fontSize: 18,
            ),
          ),
          SizedBox(
            height: 10,
          ),
          ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: Container(
              height: 278,
              width: 320,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(3),
                image: DecorationImage(
                    fit: BoxFit.fitWidth,
                    image: AssetImage(
                      "assets/images/rewardopenbg.png",
                    )),
              ),
              padding: const EdgeInsets.all(15),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CachedNetworkImage(
                    width: 110,
                    imageUrl: rewards[index].imageUrl ??
                        'https://junq-bucket.s3.ap-south-1.amazonaws.com/common/coin.png',
                    placeholder: (context, url) =>
                        Image.asset('assets/images/error-image.png'),
                  ),
                  Text(
                    'Yay!',
                    style: TextStyle(
                        color: Color(0xff20E400),
                        fontSize: 14,
                        fontWeight: FontWeight.w600),
                  ),
                  Text(rewards[index].name.toString(),
                      style: TextStyle(color: kwhite, fontSize: 18)),
                  Text(rewards[index].description.toString(),
                      style: TextStyle(color: kwhite, fontSize: 12)),
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 15),
            child: Text(
              'Congrats! ${rewards[index].name} use on your first purchase',
              softWrap: true,
              overflow: TextOverflow.fade,
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontSize: 24, fontWeight: FontWeight.w700, color: kblack),
            ),
          ),
          Text(
            'Copy code and use at checkout',
            style: TextStyle(fontSize: 12),
          ),
          SizedBox(height: 10),
          Container(
            width: 300,
            decoration: BoxDecoration(
                color: kwhite,
                borderRadius: BorderRadius.zero,
                border: Border.all(color: kblack.withOpacity(0.6))),
            child: Flex(
              direction: Axis.horizontal,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    kwidth20,
                    SelectableText(
                      rewards[index].couponCode.toString(),
                      style: TextStyle(
                          fontSize: 20,
                          color: Color(0xFF556F80),
                          letterSpacing: 1.2),
                      toolbarOptions:
                          ToolbarOptions(copy: true, selectAll: true),
                    ),
                  ],
                ),
                TextButton(
                    onPressed: () {
                      Clipboard.setData(
                          ClipboardData(text: rewards[index].couponCode));
                      customToast(message: "Offer copied to clipBoard");
                    },
                    child: const Text('Copy')),
              ],
            ),
          ),
          SizedBox(height: 10),
          Text(
            'Applicable on min. order of ₹500. This offer cannot be combind with other offers, discounts or promotions.',
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 10),
          Text(
            'Expires on ${DateFormat('dd-MMM-yyyy').format(rewards[index].validTill!)}',
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          kheight10,
          SubmitButton(
              text: 'Order Now',
              onpress: () {
                final RootViewController rootViewController = Get.find();
                rootViewController.selectedIndex = 0;
                Get.to(() => RootPage());
              })
        ],
      ),
    );
  }
}
