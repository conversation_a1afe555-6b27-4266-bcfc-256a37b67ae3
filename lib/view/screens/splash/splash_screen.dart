import 'package:geocoding/geocoding.dart';
import 'package:Rapsap/view/widgets/commons.dart';
import 'package:Rapsap/main.dart';
import 'package:Rapsap/model/user_model/usermodel.dart';
import 'package:Rapsap/view/screens/mapscreen/mappage.dart';
import 'package:Rapsap/view/screens/root_page/root_page.dart';

import '../../../controllers/accountscontroller.dart';
import '../../../controllers/wishlistcontroller.dart';
import '../../../services/firebaseservices.dart';
import '../../../services/userservices.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  final UserController _userController = Get.find<UserController>();
  @override
  void initState() {
    isAppopen();

    FocusManager.instance.primaryFocus?.unfocus();

    if (storage.read('loginstatus') != null &&
        storage.read('userdata') != null) {
      _userController.loginstatus.value = storage.read('loginstatus');

      _userController.userdata.value =
          UserData.fromJson(storage.read('userdata'));
      getUserConfig();

      setuseridanalytics();
      Get.isRegistered<AccountController>()
          ? Get.find<AccountController>()
          : Get.put(AccountController());
      final Wishlistcontroller wishlistcontroller = Get.find();
      wishlistcontroller.getwishlistitems();
    }

    if (storage.read('locationaddress') != null) {
      _userController.locationaddress.value =
          Placemark.fromMap(storage.read('locationaddress'));
    }

    debugPrint(_userController.userdata.value.data.toString());
    if(mounted){
    if (_userController.userdata.value.data?.deleted == 1) {
      Future.delayed(
          const Duration(milliseconds: 1000),
          (() => Get.to(() => const RootPage(
                data: true,
              ))));
    } else {
      Future.delayed(
          const Duration(milliseconds: 2000),
          (() => Get.to(
              () => _userController.loginstatus.isFalse
                  ? const LoginScreen()
                  : storage.read('tempPincode') == null
                      ? MapPage()
                      : const RootPage(),
              transition: Transition.fadeIn)));
    }
    }

    super.initState();
  }

  setuseridanalytics() async {
    await FirebaseService.firebaseAnalytics
        .setUserId(id: _userController.userdata.value.data!.id.toString());
  }

  isAppopen() async {
    await FirebaseService.firebaseAnalytics.logAppOpen();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Container(
            height: double.infinity,
            width: double.infinity,
            decoration: const BoxDecoration(
              color: Colors.black,
              image: DecorationImage(image: AssetImage(splashbackground)),
            ),
            child: Center(child: SvgPicture.asset(logo))));
  }

  void getUserConfig() async {
    final data = await UserService.getUserConfig("splash");
    debugPrint("data");
    if (data == true) {
      Get.to(const RootPage(
        data: true,
      ));
      return;
    }
  }
}
