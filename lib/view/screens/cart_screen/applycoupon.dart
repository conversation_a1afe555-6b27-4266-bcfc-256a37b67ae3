import 'package:Rapsap/view/screens/mapscreen/setlocation.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:Rapsap/controllers/cartcontrolller.dart';
import 'package:Rapsap/services/orderservices.dart';
import 'package:Rapsap/view/widgets/commons.dart';
import 'package:Rapsap/view/widgets/custom.dart';
import 'package:Rapsap/view/widgets/keyboardhider.dart';

import '../../../model/couponmodel.dart';
import '../../../services/databaseHelper.dart';

class CouponPage extends StatelessWidget {
  CouponPage({Key? key}) : super(key: key);
  final TextEditingController couponTextCtrl = TextEditingController();
  final UserController userController = Get.find<UserController>();
  final CartController cartcontroller = Get.find<CartController>();

  @override
  Widget build(BuildContext context) {
    return KeyboardHider(
      child: Scaffold(
        backgroundColor: kwhite,
        appBar: AppBar(
            backgroundColor: kwhite,
            toolbarHeight: 0,
            elevation: 0,
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(65),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          InkWell(
                              onTap: (() {
                                Get.back();
                              }),
                              child:
                                  SvgPicture.asset('assets/svg/iconback.svg')),
                          kwidth20,
                          const Text(
                            'Apply Coupons',
                            style: TextStyle(
                                fontSize: 20,
                                color: kblack,
                                fontWeight: FontWeight.w700),
                          ),
                        ],
                      ).paddingSymmetric(horizontal: 16, vertical: 10),
                    ],
                  ),
                  kheight10,
                  // Divider(
                  //   thickness: 4,
                  //   color: kblack.withOpacity(0.06),
                  //   height: 0,
                  // )
                ],
              ),
            )),
        body: SingleChildScrollView(
          child: Column(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  hgap(32),
                  SizedBox(
                    height: 56,
                    child: TextFormField(
                      maxLines: 1,
                      controller: couponTextCtrl,
                      decoration: InputDecoration(
                          fillColor: kblack.withOpacity(0.02),
                          filled: true,
                          hintText: 'Enter Coupon Code',
                          isDense: true,
                          suffixIcon: SizedBox(
                            height: 53,
                            child: ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                    backgroundColor: kblack,
                                    elevation: 0,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 0)),
                                onPressed: () {
                                  validateCouponCode(
                                      couponTextCtrl.text, context);
                                },
                                child: const Text('Apply')),
                          ),
                          hintStyle: TextStyle(
                              color: const Color(0xff556F80).withOpacity(0.7)),
                          suffixIconConstraints: const BoxConstraints(
                            minWidth: 90,
                          ),
                          enabledBorder: OutlineInputBorder(
                              borderSide:
                                  BorderSide(color: kblack.withOpacity(0.25)),
                              borderRadius: BorderRadius.zero),
                          border: OutlineInputBorder(
                              borderSide:
                                  BorderSide(color: kblack.withOpacity(0.25)),
                              borderRadius: BorderRadius.zero),
                          focusedBorder: OutlineInputBorder(
                              borderSide:
                                  BorderSide(color: kblack.withOpacity(0.25)),
                              borderRadius: BorderRadius.zero)),
                      onChanged: (value) {},
                    ),
                  ),
                  hgap(32),
                  const Text(
                    'Available Coupons',
                    style: TextStyle(
                        color: kblack,
                        fontSize: 18,
                        fontWeight: FontWeight.w600),
                  )
                ],
              ).paddingSymmetric(horizontal: 24),
              kheight20,
              Container(
                padding: const EdgeInsets.all(5),
                // width: MediaQuery.of(context).size.width,

                // color: Colors.green,

                child: FutureBuilder<List<Coupon>>(
                    future: OrderServices.getOffer({
                      'type': 'user',
                      'user_id': userController.userdata.value.data!.id,
                    }),
                    builder: (context, snapshot) {
                      if (snapshot.data != null) {
                        snapshot.data!.sort(
                          (b, a) => a.validTill!.compareTo(b.validTill!),
                        );

                        return ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: snapshot.data?.length,
                            itemBuilder: (context, index) {
                              Color kcolor = kblue;
                              snapshot.data![index].validFrom!
                                          .isBefore(DateTime.now()) &&
                                      snapshot.data![index].validTill!
                                          .isAfter(DateTime.now())
                                  ? kcolor = kblue
                                  : kcolor = Colors.grey;
                              return Container(
                                  child: Column(
                                children: [
                                  const Divider(
                                    color: Color(0xffF3F4F9),
                                    thickness: 2,
                                  ),
                                  hgap(24),
                                  Column(
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          DottedBorder(
                                            padding: EdgeInsets.zero,
                                            color: kblue,
                                            strokeWidth: 1,
                                            borderType: BorderType.RRect,
                                            child: Container(
                                              height: 48,
                                              decoration: BoxDecoration(
                                                color: kblue.withOpacity(0.1),
                                              ),
                                              padding: const EdgeInsets.only(
                                                  top: 10,
                                                  bottom: 10,
                                                  right: 10,
                                                  left: 2),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  CachedNetworkImage(
                                                      height: 40,
                                                      width: 40,
                                                      imageUrl: snapshot
                                                              .data![index]
                                                              .offerImage ??
                                                          'https://junq-bucket.s3.ap-south-1.amazonaws.com/common/coin.png',
                                                      placeholder: (context,
                                                              url) =>
                                                          const Image(
                                                              image: AssetImage(
                                                                  'assets/images/error-image.png'))),
                                                  kwidth5,
                                                  Text(
                                                      snapshot.data![index]
                                                          .couponCode!,
                                                      style: const TextStyle(
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          letterSpacing: 1,
                                                          color: kblack)),
                                                ],
                                              ),
                                            ),
                                          ),
                                          GetBuilder<CartController>(
                                            init: CartController(),
                                            initState: (_) {},
                                            builder: (cartController) {
                                              return index ==
                                                      cartController.apllied
                                                  ? TextButton(
                                                      onPressed: () async {},
                                                      child: Row(
                                                        children: [
                                                          Text(
                                                            'Applying',
                                                            style: GoogleFonts
                                                                .inter(
                                                                    color:
                                                                        kblack,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w500,
                                                                    fontSize:
                                                                        18),
                                                          ),
                                                        ],
                                                      ))
                                                  : TextButton(
                                                      onPressed:
                                                          kcolor == Colors.grey
                                                              ? null
                                                              : () async {
                                                                  cartcontroller
                                                                          .apllied =
                                                                      index;

                                                                  applyCoupon(
                                                                      context,
                                                                      snapshot.data![
                                                                          index]);
                                                                  await validateApplyCoupon(
                                                                      snapshot
                                                                          .data![
                                                                              index]
                                                                          .couponCode!,
                                                                      context);
                                                                  cartcontroller
                                                                          .apllied =
                                                                      1000;
                                                                },
                                                      child: Text(
                                                        'APPLY',
                                                        style:
                                                            GoogleFonts.inter(
                                                                color: kcolor,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                                fontSize: 18),
                                                      ));
                                            },
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 16),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: Text(
                                                snapshot
                                                    .data![index].description!,
                                                style: const TextStyle(
                                                    color: Color(0xFF556F80),
                                                    fontSize: 14)),
                                          )
                                        ],
                                      ),
                                      kheight20,
                                    ],
                                  ).paddingSymmetric(horizontal: 20),
                                ],
                              ));
                            });
                      } else {
                        return CustomAppShimmer(
                            child: Column(
                                children: List.generate(
                          5,
                          (index) => Column(
                            children: [
                              Container(
                                width: double.infinity,
                                height: 1,
                                color: kgrey.withOpacity(0.5),
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      DottedBorder(
                                        padding: EdgeInsets.zero,
                                        color: kblack,
                                        strokeWidth: 1,
                                        borderType: BorderType.RRect,
                                        child: Container(
                                          height: 40,
                                          width: 120,
                                          color: kgrey.withOpacity(0.5),
                                        ),
                                      ),
                                      Text('APPLY',
                                          style: GoogleFonts.inter(
                                              color: kblue,
                                              fontWeight: FontWeight.w500,
                                              fontSize: 18))
                                    ],
                                  ),
                                  kheight10,
                                  Container(
                                    decoration: BoxDecoration(
                                        color: kgrey.withOpacity(0.5),
                                        borderRadius: BorderRadius.circular(3)),
                                    height: 10,
                                    width: Get.width * 0.6,
                                  ),
                                ],
                              ).paddingSymmetric(horizontal: 24, vertical: 20),
                            ],
                          ),
                        )));
                      }
                    }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void applyCoupon(BuildContext context, Coupon coupon) async {
    // Get.close(1);;
    print(coupon);
    print('current Coupon is applied!! $coupon');

    cartcontroller.coupontext = coupon.couponCode.toString();

    couponTextCtrl.text = coupon.couponCode.toString();
    cartcontroller.update();
  }

  void validateCouponCode(couponText, context) async {
    print('apply clicked $couponText');
    await validateApplyCoupon(couponText, context);
  }

  Future validateApplyCoupon(couponText, context) async {
    var _order = await DatabaseHelper.instance.getDbOrder2();
    var reqpayload = {
      'coupon_code': couponText,
      'sub_total': _order.first.subTotal
    };
    var response = await OrderServices.vaidateCouponCode(reqpayload);
    print('response couponcode:: $response');
    if (response['success'] == false) {
      showDialog(

          // backgroundColor: Colors.transparent,
          context: context,
          builder: (context) => Dialog(
                child: Container(
                  height: 250,
                  color: Colors.white,
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.warning_rounded,
                        size: 40,
                        color: Colors.amber,
                      ),
                      response['data']['min_amount'] != null &&
                              response['data']['min_amount'].length > 0
                          ? Text(response['msg'].toString(),
                              textAlign: TextAlign.center,
                              style: const TextStyle(fontSize: 20))
                          : const Text('Coupon is invalid',
                              textAlign: TextAlign.center,
                              style: TextStyle(fontSize: 20)),
                      ElevatedButton(
                        onPressed: () {
                          Get.close(1);
                        },
                        child: const Text('OK'),
                      )
                    ],
                  ),
                ),
              ));
    } else {
      if (response['data']['offer_type'] == 'variant') {
        showDialog(
          // backgroundColor: Colors.transparent,
          context: context,
          builder: (context) => Dialog(
            child: Container(
              height: 300,
              color: Colors.white,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 120,
                    child: CachedNetworkImage(
                        fit: BoxFit.scaleDown,
                        placeholder: (context, url) => const Center(
                            child: CircularProgressIndicator.adaptive()),
                        imageUrl:
                            'https://prod-junq.s3.ap-south-1.amazonaws.com/icons8-party-popper-100.png'),
                  ),
                  const Text('Yay!'),
                  const SizedBox(height: 5),
                  Text(
                    '${couponText.toString()} applied!',
                    style: const TextStyle(
                        fontSize: 14, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    response['data']['product_name'] ?? '',
                    style: const TextStyle(
                        fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 5),
                  const Text('Product Free on this coupon code.'),
                  const SizedBox(height: 5),
                  ElevatedButton(
                      onPressed: () async {
                        await acceptCoupon(response);
                      },
                      child: const Text('Continue'))
                ],
              ),
            ),
          ),
        );
      } else {
        showDialog(
          // backgroundColor: Colors.transparent,
          context: context,
          builder: (context) => Dialog(
            child: Container(
              height: 300,
              color: Colors.white,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 120,
                    child: CachedNetworkImage(
                        fit: BoxFit.scaleDown,
                        placeholder: (context, url) => const Center(
                            child: CircularProgressIndicator.adaptive()),
                        imageUrl:
                            'https://prod-junq.s3.ap-south-1.amazonaws.com/icons8-party-popper-100.png'),
                  ),
                  const Text('Yay!'),
                  const SizedBox(height: 5),
                  Text(
                    '${couponText.toString()} applied!',
                    style: const TextStyle(
                        fontSize: 14, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    '₹${response['data']['discount'].toString()}',
                    style: const TextStyle(
                        fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 5),
                  const Text('savings with this coupon'),
                  const SizedBox(height: 5),
                  ElevatedButton(
                      onPressed: () async {
                        await acceptCoupon(response);
                      },
                      child: const Text('Continue'))
                ],
              ),
            ),
          ),
        );
      }
    }
  }

  Future clearCoupon() async {
    await DatabaseHelper.instance.setDiscountValue(0, 0, 0, '');
    await DatabaseHelper.instance.removeFreeItem(); // to remove free item
    cartcontroller.coupontext = '';
    print('cleared coupon!');
    // getProducts();
  }

  Future acceptCoupon(response) async {
    await clearCoupon(); // clear before applling new coupon.
    if (response['data']['offer_type'] == 'variant') {
      await DatabaseHelper.instance.addUpdate(
        ShopingCart(
          name: response['data']['product_name'] ?? '',
          qty: response['data']['quantity'],
          productID: response['data']['product_id'],
          price: double.parse(response['data']['price'].toString()),
          variantID: response['data']['variant_id'],
          imageURL: null,
          weight: double.parse(response['data']['weight'].toString()),
          isFree: 1,
        ),
      );

      print('added');

      await DatabaseHelper.instance.setDiscountValue(
          double.parse(response['data']['discount'].toString()),
          response['data']['offer_id'],
          double.parse(response['data']['min_amount'].toString()),
          response['data']['coupon_code']);
      // getProducts();
    } else {
      await DatabaseHelper.instance.setDiscountValue(
          double.parse(response['data']['discount'].toString()),
          response['data']['offer_id'],
          double.parse(response['data']['min_amount'].toString()),
          response['data']['coupon_code']);
    }
    print('Coupon Accepted!');

    cartcontroller.coupontext = response['data']['coupon_code'];

    couponTextCtrl.text = '';
    Get.close(2);
  }
}

class FloatingModal extends StatelessWidget {
  final Widget child;
  final Color backgroundColor;

  const FloatingModal(
      {Key? key, required this.child, required this.backgroundColor})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Material(
          color: backgroundColor,
          clipBehavior: Clip.antiAlias,
          borderRadius: BorderRadius.circular(12),
          child: child,
        ),
      ),
    );
  }
}

Future<T> showFloatingModalBottomSheet<T>({
  required BuildContext context,
  required WidgetBuilder builder,
  required Color backgroundColor,
}) async {
  final result = await showCustomModalBottomSheet(
      context: context,
      builder: builder,
      barrierColor: Colors.black.withOpacity(0.5),
      containerWidget: (_, animation, child) => FloatingModal(
            child: child,
            backgroundColor: Colors.transparent,
          ),
      expand: false);

  return result;
}
