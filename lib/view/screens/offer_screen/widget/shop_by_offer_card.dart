import 'package:flutter/material.dart';

class ShopByOfferCard extends StatelessWidget {
  const ShopByOfferCard({Key? key, required this.imageurl}) : super(key: key);
  final String imageurl;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 19, vertical: 8),
      child: Container(
        width: 377,
        height: 140,
        child: Image.asset(imageurl),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(16)),
      ),
    );
  }
}
