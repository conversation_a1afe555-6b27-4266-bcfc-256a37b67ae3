import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:Rapsap/view/screens/root_page/root_page.dart';
// import 'package:geocoder/geocoder.dart';
// import 'package:flutter_geocoder/geocoder.dart';

import 'package:Rapsap/view/widgets/commons.dart';
import 'package:group_button/group_button.dart';
import 'package:Rapsap/controllers/accountscontroller.dart';
import 'package:Rapsap/main.dart';
import 'package:Rapsap/services/accountservices.dart';
import 'package:Rapsap/view/screens/mapscreen/searchpage.dart';
import 'package:Rapsap/view/widgets/custom.dart';
import 'package:Rapsap/view/widgets/loadingscreen.dart';

import '../../../controllers/cartcontrolller.dart';
import '../../../services/userservices.dart';
import '../mapscreen/setlocation.dart';

enum addEditAddress { edit, add }

class AddressAddEditPage extends StatefulWidget {
  // final int step;
  final addEditAddress mode;

  var address;
  AddressAddEditPage({
    Key? key,
    this.address,
    required this.mode,
  }) : super(key: key);

  @override
  _AddressAddEditPageState createState() => _AddressAddEditPageState();
}

class _AddressAddEditPageState extends State<AddressAddEditPage> {
  final UserController userController = Get.find<UserController>();
  final AccountController accountController = Get.find<AccountController>();

  final _addressFormKey = GlobalKey<FormState>();
  UserController usertCTRL = Get.find();
  var address;
  final Map<String, dynamic> formData = {
    'name': '',
    'phone': '',
    'pincode': '',
    'city': '',
    'state': '',
    'landmark': '',
    'address1': '',
    'address2': '',
    'address_type': '',
    'is_default': 1,
  };
  final gcontroller = GroupButtonController(
    selectedIndex: 0,
  );

  @override
  void initState() {
    super.initState();
    setState(() {
      address = widget.address;
    });
    if (addEditAddress.add == widget.mode) {
      formData['address_type'] = "Home";

      formData['pincode'] = userController.locationaddress.value.postalCode ??
          storage.read('tempPincode') ??
          "";
      formData['state'] =
          userController.locationaddress.value.administrativeArea ?? "";
      formData['address1'] = userController.locationaddress.value.street ?? "";
      formData['address2'] =
          userController.locationaddress.value.subLocality ?? "";
      formData['city'] =
          userController.locationaddress.value.subAdministrativeArea ?? "";

      print('its add mode');
    } else if (addEditAddress.edit == widget.mode && address != null) {
      print('its edit mode ${address!.city}');
      formData['name'] = address!.name;
      formData['phone'] = address!.phone;
      formData['pincode'] = address!.pincode;
      formData['state'] = address!.state;
      formData['address1'] = address!.address1;
      formData['address2'] = address!.address2;
      formData['landmark'] = address!.landmark;
      formData['address_type'] = address!.addressType;
      formData['city'] = address!.city;
    } else {
      print('its selection mode');
    }
    // print('Current lat: ${widget.latlong.latitude}');
    // print('Current Address: ${widget.address.street}');
    // print(
    //     'widget.address.administrativeArea ::::${widget.address.administrativeArea}');
    // print('widget.address.country ::${widget.address.country}');
    // print('widget.address.isoCountryCode ::${widget.address.isoCountryCode}');
    // print('widget.address.locality ::${widget.address.locality}');
    // print('widget.address.name ::${widget.address.name}');
    // print('widget.address.postalCode ::${widget.address.postalCode}');
    // print('widget.address.street ::${widget.address.street}');
    // print(
    //     'widget.address.subAdministrativeArea ${widget.address.subAdministrativeArea}');
    // print('widget.address.subLocality ${widget.address.subLocality}');
    // print('widget.address.subThoroughfare ${widget.address.subThoroughfare}');
    // print('widget.address.thoroughfare ${widget.address.thoroughfare}');
    // if (widget.step != 2) {
  }

  @override
  Widget build(BuildContext context) {
    accountController.loading.value = false;
    return Scaffold(
      appBar: AppBar(
        elevation: 0.5,
        backgroundColor: Colors.white,
        title: Text(
          addEditAddress.edit == widget.mode ? 'Edit Address' : 'Add Address',
          style: const TextStyle(color: Colors.black87),
        ),
        iconTheme: const IconThemeData(color: Colors.black87),
      ),
      body: SingleChildScrollView(
        child: Container(
          width: MediaQuery.of(context).size.width,
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _addressFormKey,
            child: Column(
              // spacing: 0,
              // runSpacing: 10,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Set Delivery Location',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),

                // Obx(() => userController.locationaddress.value.country != null
                //     ? Row(
                //         children: [
                //           SvgPicture.asset("assets/svg/location.svg"),
                //           kwidth10,
                //           Column(
                //             crossAxisAlignment: CrossAxisAlignment.start,
                //             mainAxisAlignment: MainAxisAlignment.center,
                //             children: [
                //               Text(
                //                 userController.locationaddress.value
                //                             .subLocality ==
                //                         ""
                //                     ? userController.locationaddress.value
                //                                 .locality ==
                //                             ""
                //                         ? userController.locationaddress.value
                //                                     .subAdministrativeArea ==
                //                                 ""
                //                             ? userController.locationaddress
                //                                 .value.administrativeArea!
                //                             : userController.locationaddress
                //                                 .value.subAdministrativeArea!
                //                         : userController
                //                             .locationaddress.value.locality!
                //                     : userController
                //                         .locationaddress.value.subLocality!,
                //                 style: const TextStyle(
                //                     fontWeight: FontWeight.w700, fontSize: 16),
                //               ),
                //               Text(userController
                //                           .locationaddress.value.subLocality ==
                //                       ""
                //                   ? userController.locationaddress.value
                //                               .subAdministrativeArea ==
                //                           ""
                //                       ? userController.locationaddress.value
                //                           .administrativeArea!
                //                       : userController.locationaddress.value
                //                           .subAdministrativeArea!
                //                   : userController
                //                       .locationaddress.value.locality!)
                //             ],
                //           ),
                //           const Spacer(),
                //           InkWell(
                //             onTap: () async {
                //               try {
                //                 Get.to(() => const LoadingScreen(
                //                     text: 'Getting Location'));

                //                 final Position position =
                //                     await determinePosition();

                //                 Get.off(() => GoogleMapPage(
                //                       page: Pagestep.addaddress,
                //                       latLng: LatLng(position.latitude,
                //                           position.longitude),
                //                     ));
                //               } catch (e) {
                //                 Get.back();
                //               }
                //             },
                //             child: Container(
                //               decoration: BoxDecoration(
                //                   border: Border.all(color: kblue),
                //                   borderRadius: BorderRadius.circular(4)),
                //               child: const Text(
                //                 'Change',
                //                 style: TextStyle(color: kblue),
                //               ).paddingAll(
                //                 8,
                //               ),
                //             ),
                //           ),
                //         ],
                //       )
                //     : SizedBox()),
                const Divider(
                  height: 30,
                ),
                InputBox(
                  initial: formData['address1'],
                  lebel: 'Home details',
                  hint: 'Enter House No, Building Name',
                  type: 'text',
                  onSaved: (value) => {formData['address1'] = value},
                ),
                InputBox(
                  initial: formData['address2'],
                  lebel: 'Street, Area Details',
                  hint: 'Road Name. Area, Colony.',
                  type: 'text',
                  onSaved: (value) => {formData['address2'] = value},
                ),
                InputBox(
                  initial: formData['landmark'],
                  lebel: 'Landmark',
                  hint: 'Enter LandMark',
                  type: 'text',
                  onSaved: (value) => {formData['landmark'] = value},
                ),
                InputBox(
                  initial: formData['city'],
                  lebel: 'City',
                  hint: 'Enter City',
                  type: 'text',
                  onSaved: (value) => {formData['city'] = value},
                ),
                InputBox(
                  initial: formData['state'],
                  lebel: 'State',
                  hint: 'Enter state',
                  type: 'text',
                  onSaved: (value) => {formData['state'] = value},
                ),

                InputBox(
                  initial: formData['pincode'],
                  lebel: 'Pincode',
                  hint: 'Enter Pincode',
                  type: 'text',
                  onChanged: (value) => {formData['pincode'] = value},
                  onSaved: (value) => {formData['pincode'] = value},
                ),

                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Type of Address'),
                        kheight10,
                        GroupButton(
                          controller: gcontroller,
                          isRadio: true,
                          options: GroupButtonOptions(
                            spacing: 7,
                            buttonWidth: 55,
                            buttonHeight: 35,
                            direction: Axis.horizontal,
                            selectedTextStyle: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 13,
                              color: kblue,
                            ),
                            unselectedTextStyle: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 13,
                              color: Colors.grey,
                            ),
                            selectedColor: kblue.withOpacity(0.1),
                            unselectedColor: Colors.white,
                            selectedBorderColor: kblue,
                            unselectedBorderColor: Colors.grey,
                            borderRadius: BorderRadius.circular(5),
                            selectedShadow: <BoxShadow>[
                              const BoxShadow(color: Colors.transparent)
                            ],
                            unselectedShadow: <BoxShadow>[
                              const BoxShadow(color: Colors.transparent)
                            ],
                          ),
                          onSelected: (val, index, isSelected) {
                            switch (index) {
                              case 0:
                                formData['address_type'] = "Home";
                                break;
                              case 1:
                                formData['address_type'] = "Work";
                                break;
                              case 2:
                                formData['address_type'] = "Other";
                                break;
                              default:
                                formData['address_type'] = "Home";
                                break;
                            }
                          },
                          buttons: const ["Home", "Work", "Other"],
                        )
                      ],
                    ),
                    Column(
                      children: [
                        hgap(15),
                        const Text('Set as Default'),
                        SizedBox(
                          height: 30,
                          width: 30,
                          child: Transform.scale(
                            scale: 0.7,
                            child: CupertinoSwitch(
                              value: formData['is_default'] == 1 ? true : false,
                              activeColor: kblack,
                              onChanged: (value) {
                                print(formData['is_default']);
                                setState(() {
                                  formData['is_default'] =
                                      value == true ? 1 : 0;
                                });
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const Divider(
                  height: 30,
                ),
                InputBox(
                  initial: formData['name'],
                  lebel: 'Name',
                  hint: 'Enter Name',
                  type: 'name',
                  onSaved: (value) => {formData['name'] = value},
                ),
                InputBox(
                  initial: formData['phone'],
                  lebel: 'Phone',
                  hint: 'Enter Phone Number',
                  type: 'mobile',
                  onSaved: (value) => {formData['phone'] = value},
                ),
                const SizedBox(height: 20),
                GetBuilder<AccountController>(
                  builder: (accountController) {
                    return SizedBox(
                      height: 46,
                      width: double.infinity,
                      child: ElevatedButton(
                          style: ButtonStyle(
                            backgroundColor: MaterialStateProperty.all(kblack),
                          ),
                          onPressed: () async {
                            print(formData['pincode']);
                            checkServicableFn();
                          },
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              accountController.loading.value
                                  ? Row(
                                      children: const [
                                        SizedBox(
                                          height: 20,
                                          width: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            backgroundColor: kgrey,
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                    kblack),
                                          ),
                                        ),
                                        kwidth10,
                                      ],
                                    )
                                  : const SizedBox(),
                              const Text(
                                'Save Address',
                                style: TextStyle(fontSize: 16),
                              ),
                            ],
                          )),
                    );
                  },
                ),

                // SizedBox(height: 30),
              ],
            ),
          ),
        ),
      ),
    );
  }

  checkServicableFn() async {
    if (_addressFormKey.currentState!.validate()) {
      accountController.loading.value = true;
      accountController.update();
      _addressFormKey.currentState!.save(); //onSaved is called!
      FocusManager.instance.primaryFocus!.unfocus();

      accountController.loading.value = true;
      accountController.update();

      final pincode = formData['pincode'];

      int userType = storage.read('userType') ?? 0;
      print('userType $userType');

      var pincodeReq = {
        'pincode': pincode,
        // 'lat': currentPosition.latitude,
        // 'long': currentPosition.longitude,
      };
      var pincodeReqWToken = {
        'pincode': pincode,
        // 'lat': currentPosition.latitude,
        // 'long': currentPosition.longitude,
        "key": "JWT",
        "secret": "RAPSAP",
      };

      var dd = await UserService.checkServiceable(
        userType == 0 ? pincodeReqWToken : pincodeReq,
      );

      print('pincode result $dd');
      if (dd['success'] == true) {
        GetStorage storage = GetStorage();
        storage.write('tempPincode', pincode);
        storage.write('storeID', dd['data']['store_id']);

        if (storage.read('locationpoint') != null) {
          userController.locationpoint.value = storage.read('locationpoint');
        }

        accountController.loading.value = true;
        accountController.update();

        if (kDebugMode) {
          print(formData);
        }
        // 'user_id':null,
        // 'lattitude':'',
        // 'longitute':'',
        if (addEditAddress.add == widget.mode) {
          var req = {};
          if (userController.locationpoint.value['latitude'] == null) {
            req = {
              'user_id': userController.userdata.value.data!.id,
              ...formData
            };
          } else {
            req = {
              'user_id': userController.userdata.value.data!.id,
              'latitude': userController.locationpoint.value['latitude'],
              'longitude': userController.locationpoint.value['longitude'],
              ...formData
            };
          }
          if (userController.userdata.value.data == null) {
            return Get.to(() => const RootPage());
          } else {
            print('req:: $req');
            try {
              var res = await AccountService.createAddress(req);
              print('Res $res ');
              if (res['success'] == true) {
                print('done!!');
                final CartController cartController = Get.find();
                cartController.update(['total']);
                accountController.loading.value = true;
                accountController.update();

                accountController.getaddress();

                // Get.snackbar('Done', 'Address added successfully.');

                Future.delayed(const Duration(milliseconds: 500), () async {
                  accountController.loading.value = false;

                  Get.close(1);
                  customToast(message: "Address Created successfully");

                  // Get.off(() => SelectAddressScreen());
                  // await checkServicableFn(
                  //     context, formData['pincode']);
                });
              } else {
                accountController.loading.value = false;
                accountController.update();

                print('error in api add address');
              }
            } catch (e) {
              accountController.loading.value = false;
              accountController.update();
            }
          }
        } else if (addEditAddress.edit == widget.mode) {
          var req = {};
          if (userController.locationpoint.value['latitude'] == null) {
            req = {
              'user_id': userController.userdata.value.data!.id,
              'address_id': widget.address!.addressId,
              ...formData
            };
          } else {
            req = {
              'user_id': userController.userdata.value.data!.id,
              'latitude': userController.locationpoint.value['latitude'] ?? "",
              'longitude':
                  userController.locationpoint.value['longitude'] ?? "",
              'address_id': widget.address!.addressId,
              ...formData
            };
          }

          var res = await AccountService.updateAddress(req);
          print('res ');
          if (res['success'] == true) {
            await accountController.getaddress();
            Get.back();
            // EasyLoading.dismiss();
            Future.delayed(const Duration(milliseconds: 500), () {
              accountController.loading.value = false;
              accountController.update();
            });
            customToast(
              message: 'Address updated successfully.',
            );
          } else {
            customToast(
              message: 'Unable to update data',
            );
          }
        }
      }
    } else {
      showAlertDialog(context, "Pincode is not servicable!",
          "We are increasing our reach everyday. We shall service your locality soon");
    }
    // else {
    //   // Get.to(() => AddressPage(step: 1));
    // }
  }
}

Future<bool?> customToast(
    {required String message,
    ToastGravity gravity = ToastGravity.BOTTOM,
    Color backgroundColor = kblue}) async {
  await Fluttertoast.cancel();

  return Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_SHORT,
      gravity: gravity,
      backgroundColor: backgroundColor,
      timeInSecForIosWeb: 1,
      textColor: Colors.white,
      fontSize: 12.0);
}

class InputLabal extends StatelessWidget {
  final String title;

  const InputLabal({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: const TextStyle(
          height: 3, color: kblack, fontSize: 15, fontWeight: FontWeight.w500),
    );
  }
}

class InputBox extends StatelessWidget {
  final String hint;
  final String type;
  final String lebel;
  final String initial;
  final ValueSetter onSaved;
  final ValueSetter? onChanged;

  const InputBox(
      {Key? key,
      required this.hint,
      required this.type,
      required this.lebel,
      this.onChanged,
      required this.onSaved,
      required this.initial})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        InputLabal(title: lebel),
        SizedBox(
          child: TextFormField(
            initialValue: initial,
            maxLines: 1,
            keyboardType: type == "mobile" || lebel == "Pincode"
                ? TextInputType.phone
                : null,
            onChanged: onChanged,
            decoration: InputDecoration(
              isDense: true,
              // fillColor: kwhite,
              // filled: true,
              focusedBorder: const OutlineInputBorder(
                borderSide: BorderSide(color: kblack, width: 1),
                borderRadius: BorderRadius.zero,
              ),
              enabledBorder: const OutlineInputBorder(
                  borderSide: BorderSide(color: Color(0xff556F80), width: 1),
                  borderRadius: BorderRadius.zero),
              border: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
                  borderRadius: BorderRadius.zero),
              errorBorder: const OutlineInputBorder(
                borderSide: BorderSide(color: Colors.redAccent, width: 1),
                borderRadius: BorderRadius.zero,
              ),
              hintText: hint,
            ),
            // The validator receives the text that the user has entered.
            onSaved: onSaved,
            validator: (value) {
              if (type == 'pincode' && value!.length < 6) {
                return 'Please enter required ' + type;
              }

              if (type == 'mobile') {
                return validateMobile(value!);
              } else {
                if (value == null || value.isEmpty || value.length < 2) {
                  return 'Please enter required ' + type;
                }
                return null;
              }
            },
          ),
        ),
      ],
    );
  }
}

String? validateMobile(String value) {
  String patttern = r'(^(?:[+0]9)?[0-9]{10,12}$)';
  RegExp regExp = RegExp(patttern);
  if (value.isEmpty || value.length > 10) {
    return 'Please enter mobile number';
  } else if (!regExp.hasMatch(value)) {
    return 'Please enter valid mobile number';
  }
  return null;
}
