import 'package:Rapsap/view/screens/cart_screen/cart_screen.dart';
import 'package:Rapsap/view/widgets/commons.dart';
import 'package:Rapsap/controllers/accountscontroller.dart';
import 'package:Rapsap/model/address_model/address_model/datum.dart';
import 'package:Rapsap/view/screens/AddressPage/addaddresspage.dart';
import 'package:Rapsap/view/screens/login/widgets/button.dart';
import 'package:Rapsap/view/screens/mapscreen/searchpage.dart';
import 'package:shimmer/shimmer.dart';


Datum? selectedAddress;

class SelectAddressScreen extends StatelessWidget {
  SelectAddressScreen({Key? key}) : super(key: key);
  final UserController userController = Get.find<UserController>();
  final AccountController accountController = Get.put(AccountController());

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        Get.off(() => CartScreen(), transition: Transition.leftToRight);
        return false;
      },
      child: Scaffold(
        appBar: AppBar(
          elevation: 0,
          automaticallyImplyLeading: false,
          scrolledUnderElevation: 3,
          backgroundColor: Get.theme.scaffoldBackgroundColor,
          title: const Text(
            'Select Address',
            style: TextStyle(color: Colors.black87),
          ),
          leading: InkWell(
              onTap: (() {
                Get.off(() => CartScreen(), transition: Transition.leftToRight);
              }),
              child:
                  SvgPicture.asset('assets/svg/iconback.svg').paddingAll(17)),
          iconTheme: const IconThemeData(color: Colors.black87),
        ),
        body: Stack(
          children: [
            SingleChildScrollView(
                child: Column(
              children: [
                Column(
                  children: [
                    const CommonDivider(),
                    ListTile(
                      horizontalTitleGap: 0,
                      minLeadingWidth: 0,
                      title: const Padding(
                        padding: EdgeInsets.only(left: 8, right: 2),
                        child: Text(
                          'Add new Address',
                          style: TextStyle(fontSize: 16),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      leading: const Padding(
                          padding: EdgeInsets.only(left: 14),
                          child: Icon(
                            Icons.add_circle_outline_outlined,
                            size: 20,
                            color: kblack,
                          )),
                      trailing: GestureDetector(
                        onTap: () {
                          Get.to(() => AddressAddEditPage(
                                mode: addEditAddress.add,
                              ));
                        },
                        child: Container(
                          decoration: BoxDecoration(
                              border: Border.all(color: kblue),
                              borderRadius: BorderRadius.circular(4)),
                          child: const Text(
                            'Add Address',
                            style: TextStyle(color: kblue),
                          ).paddingAll(
                            8,
                          ),
                        ),
                      ),
                    ),
                    CommonDivider(),
                    kheight20,
                    Obx(() => accountController.addressModel.value.data!.isEmpty
                        ? Shimmer.fromColors(
                            baseColor: Colors.grey[200]!,
                            highlightColor: Colors.grey[100]!,
                            child: Container(
                              height: 80,
                              width: double.infinity,
                              color: Colors.grey,
                            ).paddingSymmetric(horizontal: 20),
                          )
                        : SelectAddressWidget(
                            accountController: accountController)),
                  ],
                ),
              ],
            )),
            Align(
              alignment: Alignment.bottomCenter,
              child: SubmitButton(
                text: 'Deliver Here',
                onpress: (() {
                  accountController.selectedAddress.value = selectedAddress!;
                  Get.off(
                    () => CartScreen(),
                  );
                }),
              ),
            ).paddingSymmetric(horizontal: 24, vertical: 24),
          ],
        ),
      ),
    );
  }
}

class SelectAddressWidget extends StatefulWidget {
  const SelectAddressWidget({
    Key? key,
    required this.accountController,
  }) : super(key: key);

  final AccountController accountController;

  @override
  State<SelectAddressWidget> createState() => _SelectAddressWidgetState();
}

class _SelectAddressWidgetState extends State<SelectAddressWidget> {
  @override
  Widget build(BuildContext context) {
    int selectedvlue = widget
        .accountController
        .addressModel
        .value
        .data![widget.accountController.addressModel.value.data!
                    .indexWhere((element) => element.isDefault == 1) !=
                -1
            ? widget.accountController.addressModel.value.data!
                .indexWhere((element) => element.isDefault == 1)
            : 0]
        .addressId!;

    selectedAddress = widget.accountController.addressModel.value.data![widget
                .accountController.addressModel.value.data!
                .indexWhere((element) => element.isDefault == 1) !=
            -1
        ? widget.accountController.addressModel.value.data!
            .indexWhere((element) => element.isDefault == 1)
        : 0];

    return StatefulBuilder(builder: (context, StateSetter setState) {
      return ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: widget.accountController.addressModel.value.data!.length,
          itemBuilder: (context, index) {
            final model =
                widget.accountController.addressModel.value.data![index];
            return Column(
              children: [
                InkWell(
                  onTap: (() {
                    setState(() {
                      selectedvlue = model.addressId!;
                      selectedAddress = model;
                    });
                  }),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          SizedBox(
                            width: 15,
                            height: 20,
                            child: Radio<int?>(
                              value: model.addressId,
                              groupValue: selectedvlue,
                              onChanged: (value) {
                                setState(() {
                                  selectedvlue = model.addressId!;
                                  selectedAddress = model;
                                });
                              },
                              fillColor: MaterialStateProperty.all(kblack),
                            ),
                          ),
                          kwidth10,
                          Text(
                            model.name!,
                            style: const TextStyle(fontWeight: FontWeight.w600),
                          ),
                          kwidth20,
                          Container(
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(4),
                                color: kblue.withOpacity(0.15)),
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 8.0, vertical: 4),
                              child: Text(
                                model.addressType!,
                                style: TextStyle(
                                    fontSize: 10,
                                    color: kblue,
                                    fontWeight: FontWeight.w400),
                              ),
                            ),
                          ),
                          Spacer(),
                          InkWell(
                            onTap: () {
                              Get.to(() => AddressAddEditPage(
                                    mode: addEditAddress.edit,
                                    address: model,
                                  ));
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                  border: Border.all(color: kblue),
                                  borderRadius: BorderRadius.circular(3)),
                              child: const Text(
                                'Edit',
                                style: TextStyle(color: kblue),
                              ).paddingSymmetric(
                                horizontal: 4,
                                vertical: 2,
                              ),
                            ),
                          ),
                        ],
                      ),
                      kheight5,
                      Text(
                        "${model.address1}, ${model.address2}, ${model.landmark},${model.city},${model.state},${model.pincode}, ${model.phone}",
                        style: TextStyle(color: Color(0xFF556F80)),
                      ),
                    ],
                  ).paddingSymmetric(horizontal: 24),
                ),
                const Divider(),
              ],
            );
          });
    });
  }
}
