import 'package:loading_indicator/loading_indicator.dart';
import 'package:Rapsap/services/accountservices.dart';
import 'package:Rapsap/view/screens/ordersection/orderdetail.dart';
import 'package:Rapsap/view/widgets/commons.dart';
import 'package:Rapsap/controllers/accountscontroller.dart';
import 'package:Rapsap/model/address_model/address_model/datum.dart';
import 'package:Rapsap/view/screens/AddressPage/addaddresspage.dart';
import 'package:Rapsap/view/screens/login/widgets/button.dart';
import 'package:Rapsap/view/screens/mapscreen/searchpage.dart';
import 'package:shimmer/shimmer.dart';

Datum? selectedAddress;

class MyAddressScreen extends StatelessWidget {
  MyAddressScreen({Key? key}) : super(key: key);
  final UserController userController = Get.find<UserController>();
  final AccountController accountController = Get.find<AccountController>();

  @override
  Widget build(BuildContext context) {
    accountController.getaddress();
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        scrolledUnderElevation: 3,
        backgroundColor: Get.theme.scaffoldBackgroundColor,
        title: const Text(
          'My Addresses',
          style: TextStyle(color: Colors.black87),
        ),
        iconTheme: const IconThemeData(color: Colors.black87),
      ),
      body: SingleChildScrollView(
          child: Column(
        children: [
          const CommonDivider(),
          ListTile(
            contentPadding: const EdgeInsets.symmetric(horizontal: 24),
            horizontalTitleGap: 5,
            minLeadingWidth: 0,
            title: const Text(
              'Add new Address',
              style: TextStyle(fontSize: 16),
              overflow: TextOverflow.ellipsis,
            ),
            leading: const Icon(
              Icons.add_circle_outline_outlined,
              size: 20,
              color: kblack,
            ),
            trailing: GestureDetector(
              onTap: () {
                Get.to(() => AddressAddEditPage(
                      mode: addEditAddress.add,
                    ));
              },
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: kblue),
                ),
                child: const Text(
                  'Add Address',
                  style: TextStyle(color: kblue),
                ).paddingAll(
                  8,
                ),
              ),
            ),
          ),
          const CommonDivider(),
          kheight20,
          Row(
            children: [
              Obx(() => Text(
                    'Saved Address${accountController.addressModel.value.data != null ? accountController.addressModel.value.data!.isEmpty ? "" : "(${accountController.addressModel.value.data!.length})" : ""} ',
                    style: const TextStyle(
                        color: kblack,
                        fontWeight: FontWeight.w600,
                        fontSize: 18),
                  )),
            ],
          ).paddingSymmetric(horizontal: 24),
          kheight20,
          Obx(() => accountController.addressModel.value.data == null
              ? Shimmer.fromColors(
                  baseColor: Colors.grey[200]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    height: 80,
                    width: double.infinity,
                    color: Colors.grey,
                  ).paddingSymmetric(horizontal: 20),
                )
              : SelectAddressWidget(accountController: accountController)),
        ],
      )),
    );
  }
}

class SelectAddressWidget extends StatefulWidget {
  const SelectAddressWidget({
    Key? key,
    required this.accountController,
  }) : super(key: key);

  final AccountController accountController;

  @override
  State<SelectAddressWidget> createState() => _SelectAddressWidgetState();
}

class _SelectAddressWidgetState extends State<SelectAddressWidget> {
  @override
  Widget build(BuildContext context) {
    return widget.accountController.addressModel.value.data == null
        ? const SizedBox()
        : widget.accountController.addressModel.value.data!.isEmpty
            ? Column(
                children: const [kheight50, kheight50, Text('No saved Address')],
              )
            : ListView.builder(
                primary: false,
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemCount:
                    widget.accountController.addressModel.value.data?.length,
                itemBuilder: (context, index) {
                  final model =
                      widget.accountController.addressModel.value.data![index];
                  return Column(
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          kheight5,
                          Row(
                            children: [
                              Text(
                                model.name!,
                                style: const TextStyle(
                                    fontWeight: FontWeight.w600),
                              ),
                              kwidth20,
                              Container(
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4),
                                    color: kblue.withOpacity(0.15)),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8.0, vertical: 4),
                                  child: Text(
                                    model.addressType!,
                                    style: const TextStyle(
                                        fontSize: 10,
                                        color: kblue,
                                        fontWeight: FontWeight.w400),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          kheight5,
                          Text(
                            "${model.address1}, ${model.address2}, ${model.landmark}, ${model.city},\n ${model.state}, ${model.pincode},\n ${model.phone}",
                            style: const TextStyle(color: Color(0xFF556F80)),
                          ),
                        ],
                      ).paddingSymmetric(horizontal: 24),
                      Divider(
                        color: kblack.withOpacity(
                          0.06,
                        ),
                        thickness: 1,
                      ).paddingSymmetric(horizontal: 24),
                      kheight10,
                      Row(
                        children: [
                          Flexible(
                              child: SizedBox(
                            height: 36,
                            child: BordersButton(
                              text: 'Edit',
                              boldness: FontWeight.w500,
                              onpress: () {
                                Get.to(() => AddressAddEditPage(
                                      mode: addEditAddress.edit,
                                      address: model,
                                    ));
                              },
                              txtcolor: kblack,
                              side: BorderSide(color: kblack.withOpacity(0.5)),
                              bgcolor: Get.theme.scaffoldBackgroundColor,
                            ),
                          )),
                          kwidth10,
                          Flexible(
                              child: SizedBox(
                            height: 36,
                            child: BordersButton(
                              boldness: FontWeight.w500,
                              text: 'Remove',
                              onpress: () {
                                Get.defaultDialog(
                                  radius: 0,
                                  title: "Delete Address",
                                  middleText:
                                      "Are you sure you want to delete this address",
                                  confirm: GetBuilder<AccountController>(
                                    builder: (accountController) {
                                      return Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: InkWell(
                                          onTap: accountController.loading.value
                                              ? null
                                              : (() async {
                                                  accountController
                                                      .loading.value = true;
                                                  accountController.update();

                                                  await AccountService
                                                      .deleteAddress({
                                                    'address_id':
                                                        model.addressId
                                                  });

                                                  await accountController
                                                      .getaddress();
                                                  accountController.update();
                                                  if (accountController
                                                      .addressModel
                                                      .value
                                                      .data!
                                                      .isEmpty) {
                                                    accountController
                                                        .selectedAddress
                                                        .value = Datum();
                                                  }

                                                  accountController
                                                      .loading.value = false;
                                                  accountController.update();

                                                  Get.back();
                                                }),
                                          child: Container(
                                              decoration: BoxDecoration(
                                                  border:
                                                      Border.all(color: kblue)),
                                              child: Padding(
                                                padding: const EdgeInsets.all(8.0),
                                                child: accountController
                                                        .loading.value
                                                    ? const SizedBox(
                                                        height: 20,
                                                        child: LoadingIndicator(
                                                          colors: [
                                                            kblue,
                                                            kblack
                                                          ],
                                                          indicatorType: Indicator
                                                              .cubeTransition,
                                                        ),
                                                      )
                                                    : const Text(
                                                        'Delete',
                                                        style: TextStyle(
                                                            color: kblue),
                                                      ),
                                              )),
                                        ),
                                      );
                                    },
                                  ),
                                  cancel: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: InkWell(
                                      onTap: () {
                                        Get.back();
                                      },
                                      child: Container(
                                          decoration: BoxDecoration(
                                              border: Border.all()),
                                          child: const Padding(
                                            padding: EdgeInsets.all(8.0),
                                            child: Text('Cancel'),
                                          )),
                                    ),
                                  ),
                                );
                              },
                              side: BorderSide(color: kblack.withOpacity(0.5)),
                              txtcolor: kblack,
                              bgcolor: Get.theme.scaffoldBackgroundColor,
                            ),
                          ))
                        ],
                      ).paddingSymmetric(horizontal: 24),
                      kheight10,
                      kheight5,
                      const ThickDivider(),
                    ],
                  );
                });
  }
}

class BordersButton extends StatelessWidget {
  const BordersButton(
      {Key? key,
      this.side = BorderSide.none,
      required this.text,
      required this.onpress,
      this.txtcolor = kwhite,
      this.bgcolor = kblack,
      this.boldness = FontWeight.w700,
      this.textsize = 18})
      : super(key: key);
  final String text;
  final VoidCallback onpress;
  final Color bgcolor;
  final Color txtcolor;
  final BorderSide side;
  final double textsize;
  final FontWeight boldness;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onpress,
      child: Text(text,
          style: TextStyle(
              color: txtcolor, fontSize: textsize, fontWeight: boldness)),
      style: ElevatedButton.styleFrom(
        side: side,
        elevation: 0,
        minimumSize: const Size(double.infinity, 56),
        backgroundColor: bgcolor,
      ),
    );
  }
}
