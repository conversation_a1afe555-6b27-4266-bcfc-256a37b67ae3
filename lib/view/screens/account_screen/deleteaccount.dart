import 'package:Rapsap/controllers/accountscontroller.dart';
import 'package:Rapsap/controllers/root_view_controller.dart';
import 'package:Rapsap/main.dart';
import 'package:Rapsap/model/user_model/usermodel.dart';
import 'package:Rapsap/services/userservices.dart';
import 'package:Rapsap/view/screens/login/widgets/button.dart';
import 'package:Rapsap/view/screens/root_page/root_page.dart';
import 'package:Rapsap/view/widgets/custom.dart';
import 'package:Rapsap/view/widgets/keyboardhider.dart';
import 'package:Rapsap/view/widgets/loadingscreen.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:geocoding/geocoding.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../controllers/cartcontrolller.dart';
import '../../../model/address_model/address_model/address_model.dart';
import '../../../model/address_model/address_model/datum.dart';
import '../../../services/databaseHelper.dart';
import '../../widgets/commons.dart';
import '../ordersection/orderdetail.dart';

class DeleteAccountScreen extends StatelessWidget {
  DeleteAccountScreen({Key? key}) : super(key: key);
  String reasonText = "";
  final AccountController accountController = Get.find();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _formKeyconfirm = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return KeyboardHider(
      child: Scaffold(
        backgroundColor: kwhite,
        appBar: AppBar(
          backgroundColor: kwhite,
          leadingWidth: 0,
          elevation: 0,
          toolbarHeight: 60,
          automaticallyImplyLeading: false,
          title: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  InkWell(
                    onTap: () => Get.back(),
                    child: const Icon(
                      Icons.arrow_back,
                      color: kblue,
                    ),
                  ),
                  const SizedBox(width: 27),
                  SizedBox(
                    width: Get.width * 0.5,
                    child: Text(
                      "Delete My Account",
                      overflow: TextOverflow.ellipsis,
                      style: GoogleFonts.dmSans(
                        fontWeight: FontWeight.w700,
                        fontSize: 20,
                        color: Colors.black,
                      ),
                    ),
                  )
                ],
              ),
              // Row(
              //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //   children: [
              //     getSvgIcon("assets/svg/cart-coverd.svg"),
              //     kwidth10,
              //   ],
              // )
            ],
          ),
          bottom: const PreferredSize(
              preferredSize: Size.fromHeight(10), child: ThickDivider()),
        ),
        body: Stack(
          children: [
            SingleChildScrollView(
              child: Container(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      Align(
                          alignment: Alignment.topLeft,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Raise a request to delete your Account',
                                style: TextStyle(
                                    fontSize: 17,
                                    fontWeight: FontWeight.w600,
                                    color: kblue),
                              ),
                              kheight10,
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: const [
                                  Text(
                                    '•',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.black,
                                      fontWeight: FontWeight.w400,
                                      height: 1.8,
                                    ),
                                  ),
                                  kwidth5,
                                  Expanded(
                                    child: Text(
                                      // "We are sad to see you go! Before proceeding  to \ndelete, please note that you will lose access \nto the following:",
                                      "We are sad to see you go! ",
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.black,
                                        fontWeight: FontWeight.w400,
                                        height: 1.8,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: const [
                                  Text(
                                    '•',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.black,
                                      fontWeight: FontWeight.w400,
                                      height: 1.8,
                                    ),
                                  ),
                                  kwidth5,
                                  Expanded(
                                    child: Text(
                                      // "We are sad to see you go! Before proceeding  to \ndelete, please note that you will lose access \nto the following:",
                                      "Before exiting the community, please note that you  will loose all the information you saved as well as access to your account.",
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.black,
                                        fontWeight: FontWeight.w400,
                                        height: 1.8,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: const [
                                  Text(
                                    '•',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.black,
                                      fontWeight: FontWeight.w400,
                                      height: 1.8,
                                    ),
                                  ),
                                  kwidth5,
                                  Expanded(
                                    child: Text(
                                      // "We are sad to see you go! Before proceeding  to \ndelete, please note that you will lose access \nto the following:",
                                      "A confirmation of your exit shall be communicated via email or whatsApp. ",
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.black,
                                        fontWeight: FontWeight.w400,
                                        height: 1.8,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          )),
                      const SizedBox(height: 10),
                      Container(
                          child: TextFormField(
                        minLines: 5,
                        maxLines: 5,
                        maxLength: 128,

                        decoration: const InputDecoration(
                          focusedBorder: OutlineInputBorder(
                              borderSide: BorderSide(color: kblack, width: 2.0),
                              borderRadius: BorderRadius.zero),
                          enabledBorder: OutlineInputBorder(
                            borderSide:
                                BorderSide(color: Colors.grey, width: 2.0),
                            borderRadius: BorderRadius.zero,
                          ),
                          border: OutlineInputBorder(
                            borderSide:
                                BorderSide(color: Colors.grey, width: 2.0),
                            borderRadius: BorderRadius.all(
                              Radius.circular(19.0),
                            ),
                          ),
                          errorBorder: OutlineInputBorder(
                            borderSide:
                                BorderSide(color: Colors.redAccent, width: 2.0),
                            borderRadius: BorderRadius.all(
                              Radius.circular(19.0),
                            ),
                          ),
                          hintText: 'Write reason of deleting account....',
                        ),
                        // The validator receives the text that the user has entered.
                        onChanged: (val) {
                          // setState(() {
                          reasonText = val;
                          // });
                        },
                        validator: (value) {
                          if (value != null && value.isEmpty) {
                            return "reason required";
                          }
                          return null;
                        },
                      )),
                      kheight30,
                    ],
                  ),
                ),
              ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        if (_formKey.currentState!.validate()) {
                          showModalBottomSheet(
                              backgroundColor: Colors.transparent,
                              context: context,
                              builder: (BuildContext context) {
                                return Container(
                                  height: 420,
                                  decoration: const BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(12),
                                          topRight: Radius.circular(12))),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Padding(
                                        padding: EdgeInsets.all(24),
                                        child: Text(
                                          "Delete Confirmation",
                                          style: TextStyle(
                                            fontSize: 18,
                                            color: kblack,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      Divider(
                                          height: 5,
                                          color: Colors.grey.shade300),
                                      Padding(
                                        padding: const EdgeInsets.all(24),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: const [
                                                Text(
                                                  "Once you delete your account",
                                                  style: TextStyle(
                                                    fontSize: 16,
                                                    color: kblue,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                                SizedBox(height: 10),
                                                DeleteAccountInstructionsUI(
                                                    title:
                                                        "You will not be able to recover data \nlater on."),
                                                DeleteAccountInstructionsUI(
                                                    title:
                                                        "Your account will no longer be accessible \nto use on any other device."),
                                              ],
                                            ),
                                            kheight50,
                                            Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.end,
                                              children: [
                                                SizedBox(
                                                  height: 48,
                                                  child: SubmitButton(
                                                      text: 'Keep Account',
                                                      onpress: () {
                                                        Get.close(2);
                                                      }),
                                                ),
                                                const SizedBox(height: 12),
                                                TextButton(
                                                  onPressed: () async {
                                                    Get.back();

                                                    await showDialog(
                                                        useRootNavigator: false,
                                                        barrierDismissible:
                                                            false,
                                                        context: context,
                                                        builder: ((context) {
                                                          return WillPopScope(
                                                            onWillPop: () =>
                                                                Future.value(
                                                                    false),
                                                            child: Dialog(
                                                              insetPadding:
                                                                  const EdgeInsets
                                                                      .all(24),
                                                              child: Container(
                                                                color: kwhite,
                                                                child: Padding(
                                                                  padding:
                                                                      const EdgeInsets
                                                                              .all(
                                                                          20.0),
                                                                  child: Column(
                                                                    mainAxisSize:
                                                                        MainAxisSize
                                                                            .min,
                                                                    children: [
                                                                      Row(
                                                                        crossAxisAlignment:
                                                                            CrossAxisAlignment.center,
                                                                        mainAxisAlignment:
                                                                            MainAxisAlignment.center,
                                                                        children: const [
                                                                          Expanded(
                                                                            child:
                                                                                Text(
                                                                              'You’re about to delete your account',
                                                                              style: TextStyle(
                                                                                fontSize: 20,
                                                                                color: kblack,
                                                                                fontWeight: FontWeight.w600,
                                                                              ),
                                                                              textAlign: TextAlign.center,
                                                                            ),
                                                                          ),
                                                                        ],
                                                                      ),
                                                                      kheight10,
                                                                      Row(
                                                                        children: const [
                                                                          Expanded(
                                                                            child:
                                                                                Text(
                                                                              'All data & features associated with your account will be permanently deleted in 30(Business) days. The information can’t be recovered once the account deleted.',
                                                                              style: TextStyle(color: Color(0xff444444)),
                                                                              textAlign: TextAlign.center,
                                                                            ),
                                                                          )
                                                                        ],
                                                                      ),
                                                                      kheight20,
                                                                      Row(
                                                                        children: const [
                                                                          Expanded(
                                                                            child:
                                                                                SelectableText.rich(
                                                                              TextSpan(
                                                                                text: 'Type ',
                                                                                style: TextStyle(color: Color(0xff444444), fontSize: 16),
                                                                                children: <TextSpan>[
                                                                                  TextSpan(
                                                                                    text: "' DELETE MY ACCOUNT ' ",
                                                                                    style: TextStyle(fontWeight: FontWeight.bold, color: kblack, fontSize: 16),
                                                                                  ),
                                                                                  TextSpan(
                                                                                    text: 'as it is',
                                                                                    style: TextStyle(color: Color(0xff444444), fontSize: 16),
                                                                                  ),
                                                                                ],
                                                                              ),
                                                                            ),
                                                                          ),
                                                                        ],
                                                                      ),
                                                                      kheight20,
                                                                      Form(
                                                                        key:
                                                                            _formKeyconfirm,
                                                                        child:
                                                                            TextFormField(
                                                                          decoration: const InputDecoration(
                                                                              isDense: true,
                                                                              focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: kblack, width: 2.0), borderRadius: BorderRadius.zero),
                                                                              enabledBorder: OutlineInputBorder(
                                                                                borderSide: BorderSide(color: kblack, width: 2.0),
                                                                                borderRadius: BorderRadius.zero,
                                                                              ),
                                                                              border: OutlineInputBorder(
                                                                                borderSide: BorderSide(color: kblack, width: 2.0),
                                                                                borderRadius: BorderRadius.zero,
                                                                              ),
                                                                              errorBorder: OutlineInputBorder(
                                                                                borderSide: BorderSide(color: Colors.redAccent, width: 2.0),
                                                                                borderRadius: BorderRadius.all(
                                                                                  Radius.circular(19.0),
                                                                                ),
                                                                              ),
                                                                              hintText: 'Type here'),
                                                                          focusNode:
                                                                              FocusNode(),
                                                                          // The validator receives the text that the user has entered.
                                                                          onChanged:
                                                                              (val) {
                                                                            // setState(() {
                                                                            reasonText =
                                                                                val;
                                                                            // });
                                                                          },
                                                                          validator:
                                                                              (value) {
                                                                            if (value!.trim() !=
                                                                                'DELETE MY ACCOUNT') {
                                                                              return "incorrect";
                                                                            } else {
                                                                              return null;
                                                                            }
                                                                          },
                                                                        ),
                                                                      ),
                                                                      kheight40,
                                                                      Obx(() =>
                                                                          SizedBox(
                                                                            height:
                                                                                46,
                                                                            child: accountController.buttonloading.value
                                                                                ? const SizedBox(
                                                                                    width: double.infinity,
                                                                                    child: ElevatedButton(
                                                                                      onPressed: null,
                                                                                      child: SizedBox(
                                                                                        height: 20,
                                                                                        child: LoadingIndicator(
                                                                                          colors: [
                                                                                            kblue,
                                                                                            kblack
                                                                                          ],
                                                                                          indicatorType: Indicator.cubeTransition,
                                                                                        ),
                                                                                      ),
                                                                                    ),
                                                                                  )
                                                                                : SubmitButton(
                                                                                    bgcolor: kwhite,
                                                                                    text: 'Confirm',
                                                                                    onpress: () async {
                                                                                      if (_formKeyconfirm.currentState!.validate()) {
                                                                                        accountController.buttonloading.value = true;
                                                                                        await Future.delayed(const Duration(seconds: 1));

                                                                                        final RootViewController rootViewController = Get.find();
                                                                                        rootViewController.selectedIndex = 0;
                                                                                        final UserController userController = Get.find();
                                                                                        final res = await UserService.deleteAccount({
                                                                                          'reason': reasonText,
                                                                                          'user_id': userController.userdata.value.data!.id
                                                                                        });
                                                                                        if (res['success']) {
                                                                                          Get.offAll(() => const RootPage());
                                                                                          accountController.buttonloading.value = false;
                                                                                          await deletestatusdialog(context);
                                                                                          final UserController userController = Get.find();
                                                                                          var userdata = await UserService.getUserDetails({
                                                                                            'user_id': userController.userdata.value.data!.id.toString(),
                                                                                          });
                                                                                          if (userdata?.data != null) {
                                                                                            userController.userdata.value = userdata!;
                                                                                            if (kDebugMode) {
                                                                                              print(userdata);
                                                                                            }
                                                                                            storage.write('userdata', UserData(data: userdata.data).toJson());
                                                                                          }
                                                                                        }
                                                                                      }
                                                                                    },
                                                                                    txtcolor: kblue,
                                                                                    side: Border.all(color: kblue),
                                                                                  ),
                                                                          )),
                                                                      kheight20,
                                                                      SizedBox(
                                                                        height:
                                                                            46,
                                                                        child:
                                                                            SubmitButton(
                                                                          onpress:
                                                                              () {
                                                                            Get.back();
                                                                          },
                                                                          text:
                                                                              'Cancel',
                                                                        ),
                                                                      )
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          );
                                                        }));
                                                  },
                                                  style: TextButton.styleFrom(
                                                    backgroundColor: Colors.red,
                                                    padding: const EdgeInsets
                                                            .symmetric(
                                                        vertical: 10,
                                                        horizontal: 25),
                                                    shape:
                                                        const RoundedRectangleBorder(
                                                      side: BorderSide(
                                                        color: Colors.red,
                                                        width: 1,
                                                      ),
                                                    ),
                                                  ),
                                                  child: Row(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: const [
                                                      Icon(
                                                        Icons.warning,
                                                        color: kwhite,
                                                      ),
                                                      SizedBox(width: 10),
                                                      Text(
                                                        'Delete Anyway',
                                                        style: TextStyle(
                                                          color: kwhite,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          fontSize: 18,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            )
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              });
                        }
                      },
                      style: ElevatedButton.styleFrom(
                          elevation: 0,
                          backgroundColor: kblack,
                          padding: const EdgeInsets.symmetric(
                              vertical: 13, horizontal: 30)),
                      // accountController.buttonloading.value
                      //     ? null
                      //     : () async {
                      //         if (_formKey.currentState!.validate()) {
                      //           accountController.buttonloading.value =
                      //               true;
                      //           await cancelOrderFn();
                      //         }
                      //       },
                      child: Text(
                        'Delete Account',
                        style: TextStyle(fontSize: 17),
                      ),
                    ),
                  ),
                ],
              ).paddingAll(24),
            )
          ],
        ),
      ),
    );
  }
}

deletestatusdialog(BuildContext context) async {
  return showDialog(
      useRootNavigator: false,
      barrierDismissible: false,
      context: context,
      builder: ((context) {
        return WillPopScope(
            onWillPop: () {
              SystemNavigator.pop();
              return Future.value(false);
            },
            child: Dialog(
                insetPadding: const EdgeInsets.all(24),
                child: Container(
                    color: kwhite,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            TextButton.icon(
                                onPressed: () {
                                  Get.to(() =>
                                      const LoadingScreen(text: "Logout"));
                                  GetStorage cartstorage = GetStorage('cart');
                                  cartstorage.erase();
                                  final cartController =
                                      Get.find<CartController>();
                                  final accountController =
                                      Get.find<AccountController>();
                                  final UserController userController =
                                      Get.find();

                                  userController.locationaddress.value =
                                      Placemark();
                                  userController.locationpoint.value.clear();
                                  userController.loginstatus.value = false;
                                  userController.userdata.value =
                                      const UserData();
                                  DatabaseHelper.instance.clearAllOrders();
                                  DatabaseHelper.instance.clearShopingCart();
                                  DatabaseHelper.instance.clearPayment();

                                  accountController.addressModel.value =
                                      AddressModel();
                                  cartController.myCartItems.value.clear();

                                  accountController.selectedAddress.value =
                                      Datum();

                                  Future.delayed(
                                      const Duration(seconds: 1),
                                      () => Get.offAll(
                                          () => const LoginScreen()));
                                  storage.erase();
                                },
                                icon: const Icon(
                                  Icons.logout_sharp,
                                  color: kblue,
                                ),
                                label: const Text(
                                  'Logout',
                                  style: TextStyle(
                                      color: kblue,
                                      fontWeight: FontWeight.w700),
                                ))
                          ],
                        ),
                        Column(mainAxisSize: MainAxisSize.min, children: [
                          kheight20,
                          SvgPicture.asset('assets/svg/deletebin.svg'),
                          hgap(30),
                          const Text('Your account is being deleted',
                              style: TextStyle(
                                  fontWeight: FontWeight.w700, fontSize: 16)),
                          hgap(14),
                          const Text(
                            'You have 30 business days to cancel your deletion request, if you don’t want to lose any account data.',
                            style: TextStyle(fontWeight: FontWeight.w500),
                          ),
                          kheight10,
                          const Text(
                              'To cancel your request kindly write an email by clicking below',
                              style: TextStyle(
                                  fontWeight: FontWeight.w600, fontSize: 14)),
                          hgap(35),
                          SubmitButton(
                              text: 'Write an Email',
                              onpress: () async {
                                if (!await launchUrl(Uri.parse(
                                    "mailto:<EMAIL>?subject=Undo Deletion of Rapsap Account &body="))) {
                                  throw "Could not launch email";
                                }
                              })
                        ]).paddingAll(20),
                      ],
                    ))));
      }));
}

class DeleteAccountInstructionsUI extends StatelessWidget {
  final String title;
  const DeleteAccountInstructionsUI({Key? key, required this.title})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 8.0, bottom: 8, top: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.only(right: 5, bottom: 8, top: 8),
            child: Icon(
              Icons.circle,
              size: 7,
              color: Colors.black,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}
