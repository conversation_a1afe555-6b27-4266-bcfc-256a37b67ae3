import 'dart:developer';
import 'dart:io';

import 'package:Rapsap/main.dart';
import 'package:Rapsap/view/screens/AddressPage/addaddresspage.dart';
import 'package:Rapsap/view/screens/account_screen/deleteaccount.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:Rapsap/model/user_model/usermodel.dart';
import 'package:Rapsap/services/userservices.dart';
import 'package:Rapsap/view/screens/account_screen/widgets/form_field_with_label.dart';
import 'package:Rapsap/view/screens/login/widgets/button.dart';

import '../../widgets/commons.dart';

class EditProfileScreen extends StatelessWidget {
  EditProfileScreen({Key? key}) : super(key: key);
  final TextEditingController _name = TextEditingController();
  final TextEditingController _mob = TextEditingController();
  final TextEditingController _mail = TextEditingController();
  final UserController userController = Get.find();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  ImagePicker imagepick = ImagePicker();

  @override
  Widget build(BuildContext context) {
    _name.text = userController.userdata.value.data!.firstName.toString();
    _mob.text = userController.userdata.value.data!.mobile.toString();

    _mail.text = userController.userdata.value.data!.email.toString();

    String? path;
    userController.image.value = '';

    // final remote = remoteConfig.getAll();
    // final data = remoteConfig.getBool('isServiceable');
    // final stringData = remoteConfig.getString('TestingGroup');

    // print(data.toString());
    // print(stringData.toString());

    // print(remote.toString());
    return Scaffold(
      appBar: AppBar(
        elevation: 1,
        scrolledUnderElevation: 3,
        backgroundColor: kwhite,
        title: const Text(
          'My Profile',
          style: TextStyle(color: Colors.black87),
        ),
        iconTheme: const IconThemeData(color: Colors.black87),
      ),
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(children: [
            kheight20,
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                children: [
                  Container(
                    color: Colors.white,
                    alignment: Alignment.center,
                    child: Obx(() => Stack(
                          alignment: Alignment.center,
                          children: [
                            userController.image.value != ""
                                ? CircleAvatar(
                                    radius: 55,
                                    backgroundImage: Image.file(
                                            File(userController.image.value))
                                        .image,
                                    onBackgroundImageError:
                                        (exception, stackTrace) {
                                      userController.image.value = "";
                                    },
                                  )
                                : userController.userdata.value.data!
                                            .userProfileImage ==
                                        null
                                    ? const CircleAvatar(
                                        backgroundColor: Colors.grey,
                                        radius: 55,
                                        child: Icon(
                                          Icons.person,
                                          size: 50,
                                          color: kblack,
                                        ),
                                      )
                                    : CircleAvatar(
                                        radius: 55,
                                        backgroundImage: Image.network(
                                                userController.userdata.value
                                                    .data!.userProfileImage)
                                            .image,
                                        onBackgroundImageError:
                                            (exception, stackTrace) {
                                          userController.image.value = "";
                                        },
                                      ),
                            Positioned(
                              bottom: 2,
                              right: 1,
                              child: InkWell(
                                onTap: () async {
                                  XFile? img = await imagepick.pickImage(
                                      source: ImageSource.gallery);
                                  if (img != null) {
                                    path = img.path;
                                    userController.image.value = img.path;
                                  }
                                },
                                child: Container(
                                  width: 30.0,
                                  height: 30.0,
                                  decoration: BoxDecoration(
                                    color: Colors.blue,
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(50.0)),
                                    border: Border.all(
                                      color: Colors.white,
                                      width: 2.0,
                                    ),
                                  ),
                                  child: const Icon(
                                    Icons.edit,
                                    color: Colors.white,
                                    size: 12,
                                  ),
                                ),
                              ),
                            )
                          ],
                        )),
                  ),
                  kheight30,
                  AddFormField(
                    fieldController: _name,
                    fieldLabel: 'Name',
                    hintText: 'Enter name',
                  ),
                  kheight10,
                  AddFormField(
                    fieldController: _mob,
                    fieldLabel: 'Mobile',
                    hintText: 'Enter phone Number',
                  ),
                  kheight10,
                  AddFormField(
                    fieldController: _mail,
                    fieldLabel: 'Email ID',
                    hintText: 'Enter email',
                  ),
                  kheight30,
                  SubmitButton(
                      text: 'Submit',
                      onpress: () async {
                        if (_formKey.currentState!.validate()) {
                          _formKey.currentState!.save();

                          ImageUploadModel? result;
                          if (path != null) {
                            result =
                                await UserService.uploadProfileImage(path!);
                            log(result!.toJson().toString());
                          }

                          var req = {
                            'user_id': userController.userdata.value.data!.id
                                .toString(),
                            'email': _mail.text,
                            'mobile': _mob.text,
                            "first_name": _name.text,
                          };

                          var res = await UserService.updateProfile(req);
                          if (res['success'] == true) {
                            customToast(
                              message: 'Details successfully updated.',
                            );
                            var userdata = await UserService.getUserDetails({
                              'user_id': userController.userdata.value.data!.id
                                  .toString(),
                            });
                            if (userdata?.data != null) {
                              userController.userdata.value = userdata!;
                              if (kDebugMode) {
                                log(userdata.toString());
                              }
                              storage.write('userdata',
                                  UserData(data: userdata.data).toJson());
                            }
                          } else {
                            customToast(
                              message: res['msg'],
                            );
                          }
                        }
                      }),
                ],
              ),
            ),
            kheight30,

            ///List Tile
            // const Divider(
            //   color: Colors.grey,
            //   thickness: 0.5,
            // ),
            // ListTile(
            //   contentPadding: EdgeInsets.zero,
            //   minLeadingWidth: 0,
            //   title: Text(
            //     'Change Password',
            //     style: GoogleFonts.inter(
            //         fontWeight: FontWeight.w500, fontSize: 16),
            //   ),
            //   trailing: const Icon(
            //     Icons.arrow_forward_ios,
            //     color: Colors.black,
            //     size: 18,
            //   ),
            // ),
            const Divider(
              height: 0,
              color: kgrey,
              thickness: 0.5,
            ),
            ListTile(
              onTap: (() {
                Get.to(() => DeleteAccountScreen());
              }),
              contentPadding: const EdgeInsets.symmetric(horizontal: 24),
              title: Text(
                'Delete Account',
                style: GoogleFonts.inter(
                    fontWeight: FontWeight.w500, fontSize: 16),
              ),
              trailing: const Icon(
                Icons.arrow_forward_ios,
                color: Colors.black,
                size: 18,
              ),
            ),
            const Divider(
              height: 0,
              color: Colors.grey,
              thickness: 0.5,
            ),
          ]),
        ),
      ),
    );
  }
}
