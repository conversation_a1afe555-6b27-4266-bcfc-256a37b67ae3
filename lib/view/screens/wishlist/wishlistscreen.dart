import 'package:Rapsap/controllers/wishlistcontroller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:Rapsap/model/wishlistmodel/wishlistmodel.dart';
import 'package:Rapsap/services/userservices.dart';
import 'package:Rapsap/view/screens/product_screen/product_screen.dart';
import 'package:shimmer/shimmer.dart';

import '../../../services/databaseHelper.dart';
import '../../../utils/constants.dart';
import '../../../controllers/cartcontrolller.dart';
import '../../../controllers/user_controller.dart';
import '../../../main.dart';
import '../Home_screen/home_screen.dart';
import '../cart_screen/cart_screen.dart';

class WishListScreen extends StatefulWidget {
  WishListScreen({Key? key}) : super(key: key);

  @override
  State<WishListScreen> createState() => _WishListScreenState();
}

class _WishListScreenState extends State<WishListScreen> {
  final UserController userController = Get.find<UserController>();
  final Wishlistcontroller wishlistcontroller = Get.find();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        titleSpacing: 0,
        elevation: 0,
        toolbarHeight: 70,
        backgroundColor: Colors.white,
        title: const Text(
          'My Wishlist',
          style: TextStyle(color: Colors.black87, fontWeight: FontWeight.w600),
        ),
        iconTheme: const IconThemeData(color: kblue),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 20),
            child: InkWell(
                onTap: () => Get.off(() => CartScreen()),
                child: SvgPicture.asset("assets/svg/cart.svg")),
          )
        ],
        bottom: PreferredSize(
          child: Divider(
            color: kblack.withOpacity(0.05),
            thickness: 4,
          ),
          preferredSize: Size.fromHeight(0),
        ),
      ),
      body: FutureBuilder<WishlistModel?>(
          future: UserService.getWishlist({
            "user_id": userController.userdata.value.data!.id.toString(),
            "store_id": storage.read('storeID') ?? 1.toString(),
          }),
          builder: (context, snapshot) {
            print(userController.userdata.value.data!.id);

            if (snapshot.connectionState == ConnectionState.waiting) {
              return Column(
                children: [
                  SizedBox(
                    child: Shimmer.fromColors(
                      baseColor: Colors.grey[200]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(
                        height: 130,
                        width: double.infinity,
                        color: Colors.grey,
                      ),
                    ),
                  ).paddingAll(20),
                  SizedBox(
                    child: Shimmer.fromColors(
                      baseColor: Colors.grey[200]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(
                        height: 130,
                        width: double.infinity,
                        color: Colors.grey,
                      ),
                    ),
                  ).paddingAll(20),
                ],
              );
            }
            wishlistcontroller.wislistItems = snapshot.data!.data;

            final model = snapshot.data!.data;

            if (model!.isEmpty) {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Center(
                    child: Text('No Items in Wishlist'),
                  )
                ],
              );
            }

            return Stack(
              children: [
                ListView.builder(
                  itemCount: model.length,
                  itemBuilder: (context, index) {
                    return itemCard(data: model[index], index: index);
                  },
                ),
                GetBuilder<CartController>(
                  builder: (cartController) {
                    return cartController.myCartItems.isEmpty
                        ? const SizedBox()
                        : Align(
                            alignment: Alignment.bottomCenter,
                            child: Container(
                                height: 65,
                                width: double.infinity,
                                decoration: BoxDecoration(
                                    color: kwhite.withOpacity(0.9),
                                    boxShadow: const [
                                      BoxShadow(
                                        color: kgrey,
                                        blurRadius: 5.0,
                                      )
                                    ]),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          '${cartController.myCartItems.fold<int>(0, (previousValue, element) => previousValue + element.qty!).toString()} item',
                                          style: const TextStyle(color: kblack),
                                        ),
                                        kheight5,
                                        FutureBuilder<double>(
                                            future: DatabaseHelper.instance
                                                .getSubTotal(),
                                            builder: (context, snapshot) {
                                              if (snapshot.data != null) {
                                                return Text(
                                                  "₹ ${snapshot.data}",
                                                  style: const TextStyle(
                                                      fontWeight:
                                                          FontWeight.w700,
                                                      fontSize: 16),
                                                );
                                              } else {
                                                return SizedBox();
                                              }
                                            })
                                      ],
                                    ),
                                    Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        MaterialButton(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 8),
                                            minWidth: 150,
                                            color: kblack,
                                            onPressed: () {
                                              Get.to(() => CartScreen());
                                            },
                                            child: const Text(
                                              'View Cart',
                                              style: TextStyle(
                                                  color: kwhite,
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 16),
                                            )),
                                      ],
                                    )
                                  ],
                                ).paddingSymmetric(horizontal: 24)),
                          );
                  },
                )
              ],
            );
          }),
    );
  }

  itemCard({required WislistItems data, required int index}) {
    final CartController controller = Get.find<CartController>();

    var discountVal = data.mrp == null
        ? 0
        : ((100 *
                    (double.parse(
                            data.mrp == null ? '0' : data.mrp.toString()) -
                        double.parse(data.price == null
                            ? '0'
                            : data.price.toString()))) /
                double.parse(data.mrp == null ? '0' : data.mrp.toString()))
            .floorToDouble();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 6),
      child: ClipRRect(
        child: Card(
          elevation: 20,
          shadowColor: kblack.withOpacity(0.2),
          child: GestureDetector(
            // onTap: (() => Get.to(() => ProductScreen(
            //       productId: data.productId,
            //       variantId: data.variantId,
            //     ))),
            child: Container(
              decoration: BoxDecoration(
                color: kwhite,
                boxShadow: [
                  BoxShadow(
                      color: kblack.withOpacity(0.2),
                      blurRadius: 2.0,
                      spreadRadius: 0.4,
                      offset: Offset(0.1, 0.5)),
                ],
              ),
              child: Column(
                children: [
                  Stack(
                    children: [
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                Expanded(
                                  flex: 2,
                                  child: Padding(
                                    padding: const EdgeInsets.all(10),
                                    child: data.image!.url == null
                                        ? Image.asset(
                                            "assets/images/error-image.png")
                                        : Image.network(data.image!.url!),
                                  ),
                                ),
                                Expanded(
                                  flex: 5,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      SizedBox(
                                        width: Get.width * 0.4,
                                        child: Text(
                                          data.name!,
                                          style: TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.w700),
                                        ),
                                      ),
                                      kheight5,
                                      Text(
                                        getweight(data.weight),
                                        style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          color: kblack.withOpacity(0.5),
                                        ),
                                      ),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Flexible(
                                            flex: 4,
                                            child: Row(
                                              children: [
                                                Text(
                                                  double.parse(data.price ?? "")
                                                      .round()
                                                      .toString(),
                                                  style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.w700,
                                                      fontSize: 18),
                                                ),
                                                kwidth10,
                                                Text(
                                                  data.mrp == "0.00" ||
                                                          data.mrp == data.price
                                                      ? ""
                                                      : '${double.parse(data.mrp!).round()}',
                                                  style: TextStyle(
                                                      decoration: TextDecoration
                                                          .lineThrough),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Flexible(
                                            flex: 3,
                                            child: StatefulBuilder(
                                                builder: (context, setState) {
                                              // DatabaseHelper.instance.getGroceries();

                                              return FutureBuilder<int>(
                                                  future: DatabaseHelper
                                                      .instance
                                                      .getQty(ShopingCart(
                                                          variantID:
                                                              data.variantId!,
                                                          productID:
                                                              data.productId!)),
                                                  builder: (context, snapshot) {
                                                    // if (snapshot.connectionState ==
                                                    //     ConnectionState.waiting) {
                                                    //   return SizedBox();
                                                    // }
                                                    if (!snapshot.hasData) {
                                                      return SizedBox();
                                                    }
                                                    return snapshot.data == 0
                                                        ? Row(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .max,
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .center,
                                                            children: [
                                                              Expanded(
                                                                child: InkWell(
                                                                  onTap:
                                                                      (() async {
                                                                    await DatabaseHelper
                                                                        .instance
                                                                        .addUpdate(
                                                                            ShopingCart(
                                                                      mrp: double.parse(data
                                                                          .mrp
                                                                          .toString()),
                                                                      name: data
                                                                          .name,
                                                                      qty: 1,
                                                                      productID:
                                                                          data.productId!,
                                                                      price: double.parse(data
                                                                          .price
                                                                          .toString()),
                                                                      variantID:
                                                                          data.variantId!,
                                                                      imageURL:
                                                                          data.image!.url ??
                                                                              '',
                                                                      weight: double.parse(data
                                                                          .weight
                                                                          .toString()),
                                                                    ));
                                                                    print(
                                                                        'added to cart');

                                                                    setState(
                                                                        () {
                                                                      // update = 1;
                                                                    });
                                                                  }),
                                                                  child: Container(
                                                                      height: 34,
                                                                      decoration: BoxDecoration(border: Border.all()),
                                                                      child: Center(
                                                                        child:
                                                                            Row(
                                                                          mainAxisAlignment:
                                                                              MainAxisAlignment.center,
                                                                          mainAxisSize:
                                                                              MainAxisSize.max,
                                                                          children: [
                                                                            SvgPicture.asset("assets/svg/add-to-cart-icon.svg"),
                                                                            const SizedBox(width: 10),
                                                                            Text(
                                                                              "Add",
                                                                              style: GoogleFonts.dmSans(
                                                                                fontSize: 14,
                                                                                color: Colors.black,
                                                                                fontWeight: FontWeight.w500,
                                                                              ),
                                                                            ),
                                                                          ],
                                                                        ),
                                                                      )),
                                                                ),
                                                              ),
                                                            ],
                                                          )
                                                        : Row(
                                                            children: [
                                                              Expanded(
                                                                child:
                                                                    Container(
                                                                  padding: const EdgeInsets
                                                                          .symmetric(
                                                                      horizontal:
                                                                          6),
                                                                  height: 35,
                                                                  color: Colors
                                                                      .black,
                                                                  child: Center(
                                                                      child:
                                                                          Row(
                                                                    mainAxisAlignment:
                                                                        MainAxisAlignment
                                                                            .spaceEvenly,
                                                                    children: [
                                                                      Flexible(
                                                                        flex: 2,
                                                                        child:
                                                                            SizedBox(
                                                                          child:
                                                                              IconButton(
                                                                            onPressed:
                                                                                () async {
                                                                              await DatabaseHelper.instance.removeUpdate(ShopingCart(
                                                                                mrp: double.parse(data.mrp.toString()),
                                                                                name: data.name,
                                                                                qty: 1,
                                                                                productID: data.productId!,
                                                                                price: double.parse(data.price.toString()),
                                                                                variantID: data.variantId!,
                                                                                imageURL: data.image?.url ?? '',
                                                                                weight: double.parse(data.weight.toString()),
                                                                              ));
                                                                              setState(() {
                                                                                // update = 1;
                                                                              });
                                                                              print('remove to cart');
                                                                            },
                                                                            icon:
                                                                                const Icon(
                                                                              Icons.remove,
                                                                              size: 15,
                                                                              color: Colors.white,
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                      kwidth5,
                                                                      GetBuilder<
                                                                          CartController>(
                                                                        init:
                                                                            CartController(),
                                                                        initState:
                                                                            (_) {},
                                                                        builder:
                                                                            (cartController) {
                                                                          return Flexible(
                                                                            flex:
                                                                                2,
                                                                            child:
                                                                                Text(
                                                                              "${snapshot.data.toString()}",
                                                                              style: GoogleFonts.dmSans(
                                                                                fontSize: 14,
                                                                                fontWeight: FontWeight.w500,
                                                                                color: Colors.white,
                                                                              ),
                                                                            ),
                                                                          );
                                                                        },
                                                                      ),
                                                                      Flexible(
                                                                        flex: 2,
                                                                        child:
                                                                            IconButton(
                                                                          onPressed:
                                                                              () async {
                                                                            await DatabaseHelper.instance.addUpdate(ShopingCart(
                                                                              mrp: double.parse(data.mrp.toString()),
                                                                              name: data.name,
                                                                              qty: 1,
                                                                              productID: data.productId!,
                                                                              price: double.parse(data.price.toString()),
                                                                              variantID: data.variantId!,
                                                                              imageURL: data.image?.url ?? '',
                                                                              weight: double.parse(data.weight.toString()),
                                                                            ));
                                                                            print("qty increased");
                                                                            setState(() {
                                                                              // update = 1;
                                                                            });
                                                                          },
                                                                          icon:
                                                                              const Icon(
                                                                            Icons.add,
                                                                            size:
                                                                                15,
                                                                            color:
                                                                                Colors.white,
                                                                          ),
                                                                        ),
                                                                      )
                                                                    ],
                                                                  )),
                                                                ),
                                                              ),
                                                            ],
                                                          );
                                                  });
                                            }),
                                          ),
                                        ],
                                      )
                                    ],
                                  ),
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          discountVal != 0
                              ? SizedBox(
                                  height: 47,
                                  child: Stack(
                                    children: [
                                      SvgPicture.asset(
                                        'assets/svg/triangle_offer.svg',
                                        color: Color(0xffE73636),
                                        height: 47,
                                      ),
                                      Positioned(
                                        bottom: 20,
                                        right: 4,
                                        left: 3,
                                        child: Transform.rotate(
                                            angle: 5.54,
                                            child: Text(
                                              '${discountVal.round()}% OFF',
                                              style: TextStyle(
                                                  color: kwhite,
                                                  fontWeight: FontWeight.w700,
                                                  fontSize: 10),
                                            )),
                                      ),
                                    ],
                                  ),
                                )
                              : SizedBox(),
                          IconButton(
                            onPressed: () async {
                              final UserController userController = Get.find();
                              await UserService.updateWishlist({
                                "update_name": "updateWishlist",
                                "user_id":
                                    userController.userdata.value.data!.id,
                                "product_id": data.productId,
                                "variant_id": data.variantId,
                              });

                              setState(() {});
                            },
                            icon: Icon(Icons.close),
                            iconSize: 20,
                            color: Colors.grey,
                          )
                        ],
                      )
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
