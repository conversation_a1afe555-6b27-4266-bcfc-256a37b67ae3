import 'package:Rapsap/controllers/root_view_controller.dart';
import 'package:flutter/services.dart';
import 'package:Rapsap/controllers/cartcontrolller.dart';
import 'package:Rapsap/controllers/ordercontroller.dart';
import 'package:Rapsap/model/order/order_list_model/order_list_model/order_list_model.dart';
import 'package:Rapsap/view/screens/cart_screen/cart_screen.dart';
import 'package:Rapsap/view/screens/login/widgets/button.dart';
import 'package:Rapsap/view/screens/ordersection/myorders.dart';
import 'package:Rapsap/view/screens/root_page/root_page.dart';
import 'package:Rapsap/view/widgets/commons.dart';

import '../../../services/databaseHelper.dart';
import '../../../services/firebaseservices.dart';
import 'PulsatingCircleBtn.dart';

class SuccessPage extends StatefulWidget {
  const SuccessPage({Key? key}) : super(key: key);

  @override
  _SuccessPageState createState() => _SuccessPageState();
}

class _SuccessPageState extends State<SuccessPage> {
  final CartController cartController = Get.find<CartController>();
  final OrderController orderController = Get.find<OrderController>();

  late Orders _order = Orders();
  String orderid = '';
  @override
  void initState() {
    HapticFeedback.vibrate();
    // TODO: implement initStatepay
    super.initState();
    getOrderDetails();
  }

  Future getOrderDetails() async {
    List<Orders> odrList = await DatabaseHelper.instance.getDbOrder();

    setState(() {
      _order = odrList[0];
      orderid = odrList[0].orderID.toString();

      print(_order.toMap());
    });
    final everyitem = cartController.myCartItems.map((e) {
      print(e.price);
      return AnalyticsEventItem(
          currency: "INR",
          itemId: e.id.toString(),
          itemName: e.name,
          price: e.price,
          quantity: e.qty);
    }).toList();

    await FirebaseService.firebaseAnalytics.logPurchase(
        currency: "INR",
        items: everyitem,
        coupon: _order.couponCode,
        value: cartController.totalprice.value);
    await FirebaseService.firebaseAnalytics.logEvent(
        name: 'Purchase_done',
        parameters: {'amount': cartController.totalprice.value});

    await DatabaseHelper.instance.clearAllOrders();
    await DatabaseHelper.instance.clearShopingCart();
    await DatabaseHelper.instance.clearPayment();
    cartController.myCartItems.clear();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: (() async {
        Get.offUntil(
            GetPageRoute(page: () => const CartScreen()), (route) => false);
        return false;
      }),
      child: Scaffold(
        body: SafeArea(
          child: Container(
            padding: const EdgeInsets.all(25),
            // color: Color(0xFFF3F4F9),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                PulsatingCircleIconButton(
                  onTap: () {},
                  icon: const Icon(
                    Icons.check_sharp,
                    color: Colors.white,
                    size: 50,
                  ),
                ),
                const SizedBox(height: 50),
                const Text(
                  'Your Order has been\n placed Successfully!',
                  style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87),
                ),
                Align(
                  alignment: Alignment.center,
                  heightFactor: 1.5,
                  child: RichText(
                    textAlign: TextAlign.center,
                    text: TextSpan(
                      text: 'Your item has been placed and is on',
                      style: const TextStyle(
                          color: kblack, fontSize: 16, height: 1.3),
                      children: [
                        const TextSpan(
                          text: '\nit\'s way to being processed.',
                        ),
                        const TextSpan(
                          text: '\nOrder id ',
                        ),
                        // ignore: unnecessary_null_comparison

                        TextSpan(
                            text: '#${orderid.toString()}',
                            style: const TextStyle(
                                fontSize: 16, fontWeight: FontWeight.bold))
                      ],
                    ),
                  ),
                ),
                Row(
                  children: [
                    Expanded(
                      child: SubmitButton(
                          text: 'My Orders',
                          onpress: () {
                            orderController.orderListModel.value =
                                OrderListModel();

                            Get.to(() => MyOrders());
                          }),
                    )
                  ],
                ),
                kheight20,
                // Row(
                //   children: [
                //     Expanded(
                //       child: SubmitButton(
                //           bgcolor: kwhite,
                //           side: Border.all(color: kblack),
                //           text: 'My Subscriptions',
                //           txtcolor: kblack,
                //           onpress: () {
                //             Get.to(() => SubscriptionCommingSoon());
                //           }),
                //     )
                //   ],
                // ),
                // kheight10,
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    TextButton(
                        onPressed: () async {
                          await DatabaseHelper.instance.clearAllOrders();
                          await DatabaseHelper.instance.clearShopingCart();
                          await DatabaseHelper.instance.clearPayment();
                          final RootViewController rootViewController =
                              Get.find();
                          rootViewController.selectedIndex = 0;

                          Get.to(() => const RootPage());
                          cartController.myCartItems.value.clear();
                        },
                        child: const Text(
                          "Continue Shopping",
                          style: TextStyle(
                              color: kblue,
                              fontSize: 18,
                              fontWeight: FontWeight.w500),
                        ))
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
