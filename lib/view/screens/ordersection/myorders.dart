import 'package:Rapsap/view/screens/Home_screen/home_screen.dart';
import 'package:Rapsap/view/screens/login/widgets/button.dart';
import 'package:Rapsap/view/screens/root_page/root_page.dart';
import 'package:flutter/foundation.dart';
import 'package:Rapsap/controllers/ordercontroller.dart';

import 'package:Rapsap/view/screens/ordersection/orderdetail.dart';
import 'package:Rapsap/view/widgets/custom.dart';
import 'package:intl/intl.dart';

import '../../widgets/commons.dart';

class MyOrders extends StatelessWidget {
  MyOrders({Key? key}) : super(key: key);
  final OrderController orderController = Get.find<OrderController>();

  @override
  Widget build(BuildContext context) {
    orderController.getOrders();
    return RefreshIndicator(
      displacement: 125,
      onRefresh: (() {
        return orderController.getOrders();
      }),
      child: Scaffold(
          appBar: AppBar(
              backgroundColor: Theme.of(context).scaffoldBackgroundColor,
              elevation: 0,
              toolbarHeight: 65,
              leadingWidth: 0,
              automaticallyImplyLeading: false,
              title: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      InkWell(
                          onTap: (() {
                            Get.back();
                          }),
                          child: getSvgIcon('assets/svg/iconback.svg')),
                      const SizedBox(width: 27),
                      SizedBox(
                        width: Get.width * 0.5,
                        child: Text(
                          "My Orders",
                          overflow: TextOverflow.ellipsis,
                          style: GoogleFonts.dmSans(
                            fontWeight: FontWeight.w700,
                            fontSize: 20,
                            color: Colors.black,
                          ),
                        ),
                      )
                    ],
                  ),
                ],
              ),
              bottom: PreferredSize(
                preferredSize: Size.zero,
                child:
                    Obx(() => orderController.orderListModel.value.data != null
                        ? orderController.orderListModel.value.data!.isNotEmpty
                            ? Divider(
                                height: 0,
                                color: kblack.withOpacity(0.06),
                                thickness: 4,
                              )
                            : const SizedBox()
                        : const SizedBox()),
              )),
          body: SizedBox(
            child: Obx(() => orderController.orderListModel.value.data != null
                ? Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      kheight10,
                      // Divider(
                      //   color: kblack.withOpacity(0.06),
                      //   thickness: 4,
                      // ),

                      orderController.orderListModel.value.data!.isEmpty
                          ? Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                kheight30,
                                SvgPicture.asset("assets/svg/emptyprders.svg")
                                    .paddingSymmetric(horizontal: 40),
                                kheight30,
                                Text(
                                  "No orders yet",
                                  style: GoogleFonts.dmSans(
                                      fontSize: 20,
                                      color: const Color(0xff6F6F6F),
                                      fontWeight: FontWeight.w700),
                                ),
                                kheight30,
                                SizedBox(
                                  width: 200,
                                  height: 50,
                                  child: SubmitButton(
                                      textsize: 20,
                                      text: 'Order now',
                                      onpress: () {
                                        Get.offAll(const RootPage(),
                                            transition:
                                                Transition.rightToLeftWithFade);
                                      }),
                                )
                              ],
                            )
                          : Expanded(
                              child: ListView.separated(
                                  physics: const BouncingScrollPhysics(),
                                  itemBuilder: (context, index) {
                                    final data = orderController
                                        .orderListModel.value.data![index];
                                    return SizedBox(
                                      child: InkWell(
                                        onTap: () {
                                          if (kDebugMode) {
                                            print("OrderDetail");
                                          }
                                          // if (orderController.orderDetailModel.value
                                          //         .data?.orderId !=
                                          //     data.orderId) {
                                          //   orderController.orderDetailModel.value =
                                          //       OrderDetailModel();
                                          // }

                                          Get.to(() => OrderDetailScreen(
                                                orderId: data.orderId,
                                              ));
                                        },
                                        child: SizedBox(
                                          child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                kheight10,
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Row(
                                                      children: [
                                                        const Text(
                                                          'Ordered on ',
                                                          style: TextStyle(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w700),
                                                        ),
                                                        Text(
                                                            "${getDateformat(data.createdAt.toString())}",
                                                            style: const TextStyle(
                                                                color: kblue,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600))
                                                      ],
                                                    ),
                                                    const Icon(
                                                      Icons.arrow_forward_ios,
                                                      size: 13,
                                                    )
                                                  ],
                                                ),
                                                kheight5,
                                                Divider(
                                                  color:
                                                      kblack.withOpacity(0.06),
                                                  thickness: 1,
                                                ),
                                                const Text(
                                                    'Your order is confirmed and will be delivered',
                                                    style: TextStyle(
                                                        fontSize: 16,
                                                        fontWeight:
                                                            FontWeight.w400)),
                                                Divider(
                                                  color:
                                                      kblack.withOpacity(0.06),
                                                  thickness: 1,
                                                ),
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Text(
                                                      '${data.orderDetails!.fold(0, (previousValue, element) => (element.quantity ?? 0)) * (data.orderDetails!.length)} item',
                                                      style: const TextStyle(
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          fontSize: 16),
                                                    ),
                                                    Text(
                                                      "₹ ${data.grandTotal!}",
                                                      style: const TextStyle(
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          fontSize: 16),
                                                    ),
                                                  ],
                                                ),
                                                hgap(1),
                                                Text(
                                                  data.orderDetails!
                                                      .map((element) =>
                                                          element.productName)
                                                      .join(', ')
                                                      .capitalize!,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  style: const TextStyle(
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w500),
                                                ),
                                                Divider(
                                                  color:
                                                      kblack.withOpacity(0.06),
                                                  thickness: 1,
                                                ),
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Container(
                                                      decoration: BoxDecoration(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(2),
                                                          border: Border.all(
                                                              color: orderStatusColor(
                                                                  data.orderStatus!))),
                                                      child: Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                    .symmetric(
                                                                horizontal: 8,
                                                                vertical: 5),
                                                        child: Text(
                                                          statusCheck(data
                                                              .orderStatus!),
                                                          style: TextStyle(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              color: orderStatusColor(
                                                                  data.orderStatus!)),
                                                        ),
                                                      ),
                                                    ),
                                                    Row(
                                                      children: [
                                                        const Text(
                                                          'Delivery: ',
                                                          style: TextStyle(
                                                              fontSize: 16,
                                                              color: kblack,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500),
                                                        ),
                                                        Text(
                                                          data.addressType ??
                                                              "",
                                                          style: const TextStyle(
                                                              fontSize: 16,
                                                              color: kblack,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600),
                                                        )
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                                kheight10,
                                              ])
                                              .paddingSymmetric(horizontal: 24),
                                        ),
                                      ),
                                    );
                                  },
                                  separatorBuilder: (context, index) => Divider(
                                        color: kblack.withOpacity(0.06),
                                        thickness: 4,
                                      ),
                                  itemCount: orderController
                                      .orderListModel.value.data!.length),
                            ),
                    ],
                  )
                : const Center(
                    child: CircularProgressIndicator.adaptive(
                      strokeWidth: 4,
                      backgroundColor: kgrey,
                      valueColor: AlwaysStoppedAnimation<Color>(kblack),
                    ),
                  )),
          )),
    );
  }

  String? getDateformat(String date) {
    var dd = DateFormat('MMM, d yyyy').format(DateTime.parse(date));

    return dd.toString();
  }
}

String statusCheck(String orderStatus) {
  switch (orderStatus.toLowerCase()) {
    case 'order_updated':
      return 'Ordered';

    case 'order_paid':
      return 'Paid';

    case 'packed':
      return 'Packed';

    case 'order_created':
      return 'Ordered';

    case 'order_cancelled':
      return 'Cancelled';

    case 'out_for_delivery':
      return 'Out for Delivery';

    case 'delivered':
      return 'Delivered';

    case 'reschedule':
      return 'Rescheduled';

    case 'arrived':
      return 'out_for_delivery';

    case 'refund_created':
      return 'refund initiated';

    case 'refund_processed':
      return 'refund initiated';

    case 'refund_failed':
      return 'refund failed';

    default:
      return 'Ordered';
  }
}

Color orderStatusColor(String orderStatus) {
  switch (orderStatus.toLowerCase()) {
    case 'order_updated':
      return const Color(0xFF2B96DC);

    case 'order_paid':
      return const Color(0xff03A74F);

    case 'packed':
      return const Color(0xFFF37A20);

    case 'order_created':
      return const Color(0xFF2B96DC);

    case 'order_cancelled':
      return const Color(0xffF1373A);

    case 'out_for_delivery':
      return const Color.fromARGB(255, 248, 122, 4);

    case 'arrived':
      return const Color.fromARGB(255, 248, 122, 4);

    case 'reschedule':
      return const Color.fromARGB(255, 248, 122, 4);

    case 'refund_created':
      return const Color(0xFFF37A20);

    case 'refund_processed':
      return const Color(0xff03A74F);

    case 'refund_failed':
      return const Color(0xffF1373A);

    case 'delivered':
      return Colors.green.shade700;

    default:
      return const Color(0xFF2B96DC);
  }
}
