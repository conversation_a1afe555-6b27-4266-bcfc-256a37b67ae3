import 'dart:developer';

import 'package:Rapsap/view/screens/ordersection/paymentmethod.dart';
import 'package:flutter/foundation.dart';
import 'package:Rapsap/view/widgets/commons.dart';
import 'package:Rapsap/controllers/accountscontroller.dart';
import 'package:Rapsap/services/orderservices.dart';
import 'package:Rapsap/view/screens/login/widgets/button.dart';
import 'package:Rapsap/view/screens/ordersection/ordersuccess.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';

import '../../../controllers/cartcontrolller.dart';
import '../../../main.dart';
import '../../../services/databaseHelper.dart';
import '../cart_screen/cart_screen.dart';

class OrderProcessing extends StatefulWidget {
  final Orders order;
  final String orderType;

  final List<ShopingCart> itemlist;

  const OrderProcessing({
    Key? key,
    required this.order,
    required this.itemlist,
    required this.orderType,
  }) : super(key: key);

  @override
  State<OrderProcessing> createState() => _OrderProcessingState();
}

class _OrderProcessingState extends State<OrderProcessing> {
  final CartController controller = Get.find<CartController>();

  final UserController userController = Get.find<UserController>();

  UserController userCTRL = Get.find();
  final AccountController accountController =
      Get.isRegistered<AccountController>()
          ? Get.find<AccountController>()
          : Get.put(AccountController());
  final _razorpay = Razorpay();
  @override
  void initState() {
    super.initState();

    createOrder(widget.orderType);

    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
  }

  

  createOrder(orderType) async {
    Orders order = widget.order;
    var tax = order.tax ?? 0.0;
    final UserController usertCTRL = Get.find();

    log("START");
    var orderpayload = {
      "user_id": usertCTRL.userdata.value.data!.id.toString(),
      "brand_id": 0,
      "discount": order.discount ?? 0.0,
      "grand_total": getGrandTotal(order), // need to change here
      "gst": ((order.subTotal! * tax) / 100).floorToDouble(),
      "orderDetails": widget.itemlist.map((e) {
        return {
          "product_id": e.productID,
          "quantity": e.qty,
          "variant_id": e.variantID,
          "buying_price": e.price,
        };
      }).toList(),
      "address_id": accountController.selectedAddress.value.addressId,
      if (orderType != "COD") "order_type": orderType,
      "store_id": storage.read('storeID') ?? 0,
      "sub_total": order.subTotal,
      "offer_id": order.offerID ?? 0,
      "delivery_cost": getGrandTotal(order) >
              double.parse(configModel.deliveryFeeThreshold.toString())
          ? 0
          : double.parse(configModel.deliveryFee.toString())
    };
    print('orderpayload $orderpayload');
    print("orderid==${order.orderID}");

    if (order.orderID == null) {
      log("order created");
      try {
        var ordRes = await OrderServices.createOrder(orderpayload);
        print('createOrder result: $ordRes ');
        if (ordRes['success'] == true) {
          int id = await DatabaseHelper.instance
              .setOrderID(ordRes['data']['order_id']);
          List<Orders> odrList = await DatabaseHelper.instance.getDbOrder1();

          order = odrList[0];
          if (orderType == "COD") {
            Get.off(
                () => PaymentMethod(order: order, itemlist: widget.itemlist));
          } else {
            initiatePayment(order, usertCTRL);
          }
        }
      } catch (e) {
        controller.buttonloading = false;
        controller.update();
        // EasyLoading.dismiss();
        print('error while create order');
      }
    } else {
      log("order_updated");
      var orderpayload = {
        "order_id": order.orderID,
        "updated_by": usertCTRL.userdata.value.data!.id.toString(),
        "brand_id": 0,
        "discount": order.discount ?? 0.0,
        "grand_total": getGrandTotal(order), // need to change here
        // "gst": ((_order.subTotal! * _order.tax!) / 100).floorToDouble(),
        "gst": 0,
        "orderDetails": widget.itemlist.map((e) {
          return {
            "product_id": e.productID,
            "quantity": e.qty,
            "variant_id": e.variantID,
            "buying_price": e.price,
          };
        }).toList(),
        "address_id": accountController.selectedAddress.value.addressId,
        "store_id": storage.read('storeID') ?? 1,
        "sub_total": order.subTotal,
        "status": "order_updated",
        if (orderType != "COD") "order_type": orderType,
        "delivery_cost": getGrandTotal(order) >
                double.parse(configModel.deliveryFeeThreshold.toString())
            ? 0
            : double.parse(configModel.deliveryFee.toString())
      };
      print("orderpayload=${orderpayload.toString()}");
      var ordUpdateRes = await OrderServices.updateOrder(orderpayload);
      print('ordUpdateRes result: $ordUpdateRes');
      if (ordUpdateRes['success'] == true) {
        if (orderType == "COD") {
          Get.off(() => PaymentMethod(order: order, itemlist: widget.itemlist));
        } else {
          initiatePayment(order, usertCTRL);
        }
      }
    }
  }

  initiatePayment(order, usertCTRL) async {
    var initPayload = {
      "order_id": order.orderID,
      "grand_total": getGrandTotal(order),
      "user_id": usertCTRL.userdata.value.data!.id.toString()
    };

    var payment = await DatabaseHelper.instance.getPaymentDetails();
    print('payment $payment');
    if (payment.length > 0) {
      print("payment Existing");

      var options = {
        'key': payment[0]['razorKey'],
        'amount': getGrandTotal(order),
        'image': rapsaplogobase64,

        'name': 'RapSap',
        'order_id': payment[0]['razorInvoiceID'],
        'description': 'RapSAp',
        "theme.color": "#000000",

        // 'timeout': 60, // in seconds
        'prefill': {
          'contact': usertCTRL.userdata.value.data!.mobile,
          'email': usertCTRL.userdata.value.data!.email
        },
        "notes": {
          "order_id": order.orderID,
          "grand_total": getGrandTotal(order),
          "user_id": usertCTRL.userdata.value.data!.id.toString()
        }
      };
      controller.buttonloading = false;
      controller.update();

      _razorpay.open(options);
    } else {
      var payOption = await OrderServices.initiatePayment(initPayload);
      // print('initiatePayment:: ${payOption}');
      // print('razorpay_key_id:: ${payOption['data']['razorpay_key_id']}');
      if (payOption['success'] == true) {
        print(" payment new");

        // EasyLoading.dismiss();
        print('payOption $payOption');
        DatabaseHelper.instance.setPaymentDetails(
            payOption['data']['order_id'].toString(),
            payOption['data']['id'],
            payOption['data']['status'],
            payOption['data']['razorpay_key_id']);
        var options = {
          'key': payOption['data']['razorpay_key_id'],
          'amount': getGrandTotal(order),
          'image': rapsaplogobase64,
          'order_id': payOption['data']['razorpay_id'],
          'description': 'Rapsap',
          'name': 'RapSap',
          "theme.color": "#000000",

          // 'timeout': 60, // in seconds
          'prefill': {
            'contact': usertCTRL.userdata.value.data!.mobile,
            'email': usertCTRL.userdata.value.data!.email
          },
          "notes": {
            "order_id": order.orderID,
            "grand_total": getGrandTotal(order),
            "user_id": usertCTRL.userdata.value.data!.id.toString()
          }
        };
        controller.buttonloading = false;
        controller.update();

        _razorpay.open(options);
      } else {
        controller.buttonloading = false;
        controller.update();

        print('Existing order:: $payOption');
      }
    }
  }

  @override
  void dispose() {
    super.dispose();
    _razorpay.clear();
  }

  void _handlePaymentSuccess(PaymentSuccessResponse response) async {
    // Do something when payment succeeds
    if (kDebugMode) {
      print('response.orderId:: ${response.orderId}');
      print('response.paymentId:: ${response.paymentId}');
      print('response.signature:: ${response.signature}');
    }

    if (response.paymentId != null && response.signature != null) {
      // Get.snackbar('Success', 'Payment is capture successfully!');
      // EasyLoading.dismiss();

      Get.off(() => const SuccessPage());
    }
  }

  void _handlePaymentError(PaymentFailureResponse response) async {
    // Do something when payment fails
    if (kDebugMode) {
      print('_handlePaymentError:: ${response.code}');
      print('_handlePaymentError:: ${response.message}');
    }
    Get.back();

    await showDialog(
        context: context,
        builder: (context) {
          return Dialog(
            insetPadding: const EdgeInsets.all(defaultpadding),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SvgPicture.asset('assets/svg/paymentfailed.svg'),
                kheight20,
                Text(
                  'Oops! Payment Failed',
                  style: headTextStyle,
                ),
                kheight10,
                // Text(
                //   'Sorry! payment unsuccessfull, Please try again',
                // ),
                Text(
                  '${response.message}',
                ),
                kheight30,
                SubmitButton(
                  height: 45,
                  text: 'Try Again',
                  onpress: () {
                    Get.close(1);
                  },
                  textsize: 16,
                ),
                kheight20,
              ],
            ).paddingAll(defaultpadding),
          );
        });
    // EasyLoading.dismiss();
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    // Do something when an external wallet was selected
    print('_handleExternalWallet:: $response');
    // EasyLoading.dismiss();
  }

  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
          child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator.adaptive(
            strokeWidth: 5,
            backgroundColor: kgrey,
            valueColor: AlwaysStoppedAnimation<Color>(kblack),
          ),
          kheight20,
          Text(
            'Processing',
            style: TextStyle(fontWeight: FontWeight.bold),
          )
        ],
      )),
    );
  }
}
double getGrandTotal(Orders order) {
    print('grandtotal');
    double? discount = order.discount ?? 0.0;
    log(discount.toString());
    debugPrint(order.subTotal.toString());
    double grandtotal = (order.subTotal! - discount);
    // return grandtotal.floorToDouble();
    print(grandtotal);
    if (grandtotal >
        double.parse(configModel.deliveryFeeThreshold.toString())) {
      return grandtotal.floorToDouble();
    }
    return (grandtotal + double.parse(configModel.deliveryFee.toString()))
        .floorToDouble();
  }