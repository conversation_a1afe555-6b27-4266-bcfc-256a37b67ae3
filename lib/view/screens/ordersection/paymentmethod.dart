import 'dart:developer';

import 'package:Rapsap/view/screens/AddressPage/addaddresspage.dart';
import 'package:Rapsap/view/screens/ordersection/orderdetail.dart';
import 'package:Rapsap/view/screens/ordersection/ordersuccess.dart';
import 'package:Rapsap/view/widgets/animationviewcart.dart';
import 'package:flutter/services.dart';
import 'package:loading_indicator/loading_indicator.dart';

import '../../../controllers/accountscontroller.dart';
import '../../../controllers/cartcontrolller.dart';
import '../../../main.dart';
import '../../../services/databaseHelper.dart';
import '../../../services/orderservices.dart';
import '../../widgets/commons.dart';
import '../../widgets/custom.dart';
import '../cart_screen/cart_screen.dart';
import '../login/widgets/button.dart';
import 'orderprocessing.dart';

List<PaymentOption> paymentOptions = [
  PaymentOption('Debit/Credit Card'),
  PaymentOption('Net Banking'),
  PaymentOption('Wallet'),
  PaymentOption('UPI'),
  PaymentOption('COD'),
];

class PaymentMethod extends StatelessWidget {
  final Orders order;

  final List<ShopingCart> itemlist;
  PaymentMethod({Key? key, required this.order, required this.itemlist})
      : super(key: key);

  PaymentOption? _selectedOption =
      paymentOptions.firstWhere((element) => element.title == "COD");
  final AccountController accountController =
      Get.isRegistered<AccountController>()
          ? Get.find<AccountController>()
          : Get.put(AccountController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: customAppBar(
          title: "Payment method",
          iconButton: IconButton(
              onPressed: () {
                Get.back();
              },
              icon: SvgPicture.asset('assets/svg/iconback.svg'))),
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 10),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: SvgPicture.asset(
                    "assets/svg/paymentprocess.svg",
                  ),
                ),
                const SizedBox(height: 10),
                const ThickDivider(),
                const Padding(
                  padding: EdgeInsets.all(defaultpadding),
                  child: Text(
                    'Payment Options',
                    textAlign: TextAlign.start,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                StatefulBuilder(
                  builder: (BuildContext context, StateSetter setState) {
                    return ListView.builder(
                      shrinkWrap: true,
                      itemCount: paymentOptions.length,
                      itemBuilder: (context, index) {
                        final paymentOption = paymentOptions[index];

                        return SizedBox(
                          height: 50,
                          child: RadioListTile(
                            title: Text(
                              paymentOption.title,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight
                                    .bold, // Change font weight to bold.
                              ),
                            ),
                            value: paymentOption,
                            groupValue: _selectedOption,
                            onChanged: (value) {
                              if (paymentOption.title != 'COD') {
                                customToast(
                                    message:
                                        'This Payment option is currently not available .Please proceed with COD ');
                              } else {
                                setState(() {
                                  _selectedOption =
                                      (paymentOption.title == 'COD'
                                          ? value
                                          : null) as PaymentOption?;
                                });
                              }
                            },
                            controlAffinity: ListTileControlAffinity.leading,
                            activeColor: kblue, // Customize the active color.
                            secondary: paymentOption.title == 'COD'
                                ? null
                                : const Icon(Icons.lock,
                                    color:
                                        Colors.grey), // Disable other options.
                            tileColor: paymentOption.title == 'COD'
                                ? null
                                : Colors.grey[200], // Disable other options.
                          ),
                        );
                      },
                    );
                  },
                ),
                Container(
                  color: kwhite,
                  child: Column(
                    children: [
                      kheight20,
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: defaultpadding),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Price Details',
                              style: TextStyle(
                                  fontWeight: FontWeight.w700, fontSize: 18),
                            ),
                            kheight20,
                            PriceRowsWidget(
                              amount: '₹${order.subTotal ?? ""}',
                              title: 'Price ( ${itemlist.length} ) Items',
                            ),
                            hgap(16),
                            Column(
                              children: [
                                PriceRowsWidget(
                                  amount: order.discount.toString() == "null"
                                      ? "0.0"
                                      : order.discount.toString(),
                                  title: 'Discount',
                                ),
                                hgap(16),
                                PriceRowsWidget(
                                  amount: getGrandTotal(order) >
                                          double.parse(configModel
                                              .deliveryFeeThreshold
                                              .toString())
                                      ? 'Free'
                                      : "₹ ${double.parse(configModel.deliveryFee.toString())}",
                                  title: 'Delivery fee',
                                ),
                                hgap(16),
                                PriceRowsWidget(
                                  amount: (order.tax ?? 0).toString(),
                                  title: 'Taxes',
                                ),
                                kheight20,
                                SizedBox(
                                  width: double.infinity,
                                  height: 1,
                                  child: Container(
                                    decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                            begin: Alignment.topLeft,
                                            colors: [
                                          kblue.withOpacity(0.05),
                                          kblue.withOpacity(0.1),
                                          kblue.withOpacity(0.2),
                                          kblue.withOpacity(0.3),
                                          kblue.withOpacity(0.4),
                                          kblue.withOpacity(0.3),
                                          kblue.withOpacity(0.2),
                                          kblue.withOpacity(0.1),
                                          kblue.withOpacity(0.05),
                                        ])),
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'Total Amount',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                                Text(
                                  '₹${getGrandTotal(order)}',
                                  style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w700),
                                )
                              ],
                            ).paddingSymmetric(vertical: 20),
                          ],
                        ),
                      ),
                      hgap(180)
                    ],
                  ),
                )
              ],
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: AnimatedCartCard(
                height: 70,
                child: Container(
                  color: kblue,
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: defaultpadding),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Flexible(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Flexible(
                                child: Text(
                                  'Total Amount',
                                  style: TextStyle(
                                    color: kwhite,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                              Flexible(
                                child: Text(
                                  '₹${getGrandTotal(order)}',
                                  style: const TextStyle(
                                      color: kwhite,
                                      fontSize: 18,
                                      fontWeight: FontWeight.w700),
                                ),
                              )
                            ],
                          ),
                        ),
                        GetBuilder<CartController>(builder: (controller) {
                          return controller.buttonloading
                              ? const SizedBox(
                                  height: 20,
                                  child: LoadingIndicator(
                                    colors: [kblue, kblack],
                                    indicatorType: Indicator.cubeTransition,
                                  ),
                                ).paddingOnly(right: 20)
                              : MaterialButton(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 40),
                                  textColor: kwhite,
                                  color: kblack,
                                  onPressed: () async {
                                    if (_selectedOption != null) {
                                      HapticFeedback.vibrate();

                                      HapticFeedback.vibrate();

                                      controller.buttonloading = true;
                                      controller.update();
                                      updateOrder("COD", context);
                                    } else {
                                      customToast(
                                          message:
                                              "Please select payment option");
                                    }

                                    // createOrderNavigate();
                                  },
                                  child: const Text(
                                    'Confirm',
                                    style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500),
                                  ),
                                );
                        })
                      ],
                    ),
                  ).paddingOnly(
                      top: GetPlatform.isIOS ? 25 : 15,
                      bottom: GetPlatform.isIOS ? 35 : 20),
                )),
          )
        ],
      ),
    );
  }

  updateOrder(orderType, context) async {
    final UserController usertCTRL = Get.find();

    log("order_updated");
    var orderpayload = {
      "order_id": order.orderID,
      "updated_by": usertCTRL.userdata.value.data!.id.toString(),
      "brand_id": 0,
      "discount": order.discount ?? 0.0,
      "grand_total": getGrandTotal(order), // need to change here
      // "gst": ((_order.subTotal! * _order.tax!) / 100).floorToDouble(),
      "gst": 0,
      "orderDetails": itemlist.map((e) {
        return {
          "product_id": e.productID,
          "quantity": e.qty,
          "variant_id": e.variantID,
          "buying_price": e.price,
        };
      }).toList(),
      "address_id": accountController.selectedAddress.value.addressId,
      "store_id": storage.read('storeID') ?? 1,
      "sub_total": order.subTotal,
      "status": "order_updated",
      "order_type": orderType,
      "delivery_cost": getGrandTotal(order) >
              double.parse(configModel.deliveryFeeThreshold.toString())
          ? 0
          : double.parse(configModel.deliveryFee.toString())
    };
    print("orderpayload=${orderpayload.toString()}");
    var ordUpdateRes = await OrderServices.updateOrder(orderpayload);
    if (ordUpdateRes['success'] == true) {
      Get.to(() => const SuccessPage());
    } else {
      Get.back();

      await showDialog(
          context: context,
          builder: (context) {
            return Dialog(
              insetPadding: const EdgeInsets.all(defaultpadding),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset('assets/svg/paymentfailed.svg'),
                  kheight20,
                  Text(
                    'Oops! Order Failed',
                    style: headTextStyle,
                  ),
                  kheight10,
                  // Text(
                  //   'Sorry! payment unsuccessfull, Please try again',
                  // ),
                  const Text(
                    "Order Failed",
                  ),
                  kheight30,
                  SubmitButton(
                    height: 45,
                    text: 'Try Again',
                    onpress: () {
                      Get.close(1);
                    },
                    textsize: 16,
                  ),
                  kheight20,
                ],
              ).paddingAll(defaultpadding),
            );
          });
    }
  }
}

AppBar customAppBar({required String title, required IconButton iconButton}) {
  return AppBar(
    backgroundColor: Colors.white,
    leadingWidth: 0,
    elevation: 0,
    toolbarHeight: 60,
    automaticallyImplyLeading: false,
    title: Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            iconButton,
            const SizedBox(width: 27),
            SizedBox(
              width: Get.width * 0.5,
              child: Text(
                title,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(
                  fontWeight: FontWeight.w700,
                  fontSize: 20,
                  color: Colors.black,
                ),
              ),
            )
          ],
        ),
      ],
    ),
    bottom: const PreferredSize(
      preferredSize: Size.fromHeight(10),
      child: ThickDivider(),
    ),
  );
}

class PaymentOption {
  String title;
  PaymentOption(this.title);
}
