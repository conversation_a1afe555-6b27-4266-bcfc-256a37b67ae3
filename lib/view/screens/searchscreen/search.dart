import 'dart:async';

import 'package:Rapsap/main.dart';
import 'package:Rapsap/model/searchModel/search_model/datum.dart';
import 'package:Rapsap/model/searchModel/search_model/search_model.dart';
import 'package:Rapsap/view/screens/cart_screen/cart_screen.dart';
import 'package:Rapsap/view/widgets/buttonanimation.dart';
import 'package:Rapsap/view/widgets/commons.dart';
import 'package:Rapsap/controllers/searchcontroller.dart' as sw;
import 'package:Rapsap/view/screens/product_screen/product_screen.dart';
import 'package:Rapsap/view/widgets/keyboardhider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:shimmer/shimmer.dart';
import 'package:staggered_grid_view_flutter/widgets/staggered_grid_view.dart';
import 'package:staggered_grid_view_flutter/widgets/staggered_tile.dart';

import '../../../controllers/cartcontrolller.dart';
import '../../../services/databaseHelper.dart';
import '../../../services/firebaseservices.dart';
import '../../widgets/animationviewcart.dart';
import '../login/widgets/button.dart';
import '../mapscreen/setlocation.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({Key? key}) : super(key: key);

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  int page = 1;
  final sw.SearchController controller = Get.put(sw.SearchController());

  final ScrollController scrollController = ScrollController();

  String query = "";

  final _debouncer = Debouncer(milliseconds: 1000);

  @override
  void initState() {
    scrollController.addListener(() {
      var nextPageTrigger =
          scrollController.position.maxScrollExtent - Get.height * 0.2;
      if (scrollController.position.pixels > nextPageTrigger &&
          controller.nextpageloading == false &&
          controller.searchtotal.value >
              controller.searchresults.value.data!.length) {
        controller.nextpageloading = true;
        controller.update();
        getProducts();
      }
      // else {
      //   controller.nextpageloading = false;
      //   controller.update();
      // }
    });
    // TODO: implement initState
    super.initState();
  }

  getProducts() {
    controller.getproducts(
        query: controller.textEditingController.value.text, page: page++);
  }

  @override
  Widget build(BuildContext context) {
    final searchfocus = FocusNode();

    // final TextEditingController textEditingController = TextEditingController();
    controller.searchlist.value = storage.read('searchlist') ?? [];

    return Scaffold(
      backgroundColor: kwhite,
      appBar: AppBar(
          automaticallyImplyLeading: false,
          toolbarHeight: 80,
          leadingWidth: 0,
          titleSpacing: 0,
          elevation: 0,
          backgroundColor: kwhite,
          title: SizedBox(
            height: 45,
            child: TextField(
              autofocus: true,
              focusNode: searchfocus,
              textInputAction: TextInputAction.search,
              controller: controller.textEditingController.value,
              onChanged: ((value) {
                searchchanged(value);
              }),
              maxLines: 1,
              textAlignVertical: TextAlignVertical.center,
              decoration: InputDecoration(
                contentPadding: const EdgeInsets.symmetric(horizontal: 12),
                // isDense: true,
                prefixIcon: InkWell(
                  onTap: (() {
                    Get.back();
                  }),
                  child: Padding(
                    padding: const EdgeInsets.all(14),
                    child: SvgPicture.asset("assets/svg/iconback.svg"),
                  ),
                ),
                suffix: InkWell(
                    onTap: (() {
                      controller.textEditingController.value.clear();
                      controller.searchresults.value = SearchModel();
                    }),
                    child: const Icon(
                      Icons.close,
                      color: kgrey,
                      size: 20,
                    )),
                hintText: "Search for products...",
                hintStyle: GoogleFonts.dmSans(fontSize: 15),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(2),
                  borderSide: const BorderSide(
                    color: Color(0xffC5C8CD),
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(2),
                  borderSide: const BorderSide(
                    width: 1,
                    color: Color(0xffC5C8CD),
                  ),
                ),
              ),
            ).paddingSymmetric(horizontal: defaultpadding),
          )),
      body: KeyboardHider(
        child: Stack(
          children: [
            SingleChildScrollView(
                controller: scrollController,
                child: Obx(() => Column(
                      children: [
                        controller.searchLoading.value
                            ? Row(
                                children: [
                                  SizedBox(
                                    height: 20,
                                    width: 20,
                                    child: CircularProgressIndicator.adaptive(
                                      valueColor: AlwaysStoppedAnimation(
                                          Colors.grey.shade300),
                                    ),
                                  ),
                                  kwidth20,
                                  const Text('Searching'),
                                ],
                              ).paddingAll(24)
                            : controller.searchresults.value.data != null &&
                                    controller.textEditingController.value.text
                                        .isNotEmpty
                                ? controller.searchresults.value.data!.isEmpty
                                    ? SizedBox(
                                        height: Get.height * 0.5,
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            SvgPicture.asset(
                                                'assets/svg/search-result.svg'),
                                            kheight20,
                                            const Text(
                                              'Opps!, no results found',
                                              style: TextStyle(
                                                  fontSize: 24,
                                                  fontWeight: FontWeight.w600),
                                            ),
                                            kheight30,
                                            const Text(
                                              'Please check the spelling or try searching for a different keyword',
                                              style: TextStyle(
                                                  fontSize: 16,
                                                  color: Color(0xff556F80),
                                                  fontWeight: FontWeight.w400),
                                              textAlign: TextAlign.center,
                                            ).paddingSymmetric(horizontal: 50),
                                            kheight30,
                                            SizedBox(
                                                width: Get.width * 0.4,
                                                height: 45,
                                                child: SubmitButton(
                                                    text: 'Search Again',
                                                    onpress: () {
                                                      controller.searchresults
                                                              .value =
                                                          SearchModel();
                                                      controller
                                                          .textEditingController
                                                          .value
                                                          .clear();
                                                      searchfocus
                                                          .requestFocus();
                                                    }))
                                          ],
                                        ),
                                      )
                                    : Container(
                                        color: kwhite,
                                        child: Column(
                                          children: [
                                            Row(
                                              children: [
                                                Text(
                                                    '${controller.searchresults.value.meta!.total} results of ',
                                                    style: const TextStyle(
                                                      color: kblack,
                                                      fontSize: 16,
                                                    )),
                                                Text(
                                                  '"$query"',
                                                  style: const TextStyle(
                                                      color: kblack,
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w700),
                                                ),
                                              ],
                                            ).paddingSymmetric(
                                                horizontal: 16, vertical: 8),
                                            GetBuilder<sw.SearchController>(
                                              builder: (controller) {
                                                return Column(
                                                  children: [
                                                    StaggeredGridView
                                                        .countBuilder(
                                                            physics:
                                                                const NeverScrollableScrollPhysics(),
                                                            shrinkWrap: true,
                                                            itemCount: controller
                                                                .searchresults
                                                                .value
                                                                .data!
                                                                .length,
                                                            crossAxisCount: 2,
                                                            staggeredTileBuilder:
                                                                (index) =>
                                                                    const StaggeredTile
                                                                        .fit(1),
                                                            itemBuilder:
                                                                (context,
                                                                    index) {
                                                              final model = controller
                                                                  .searchresults
                                                                  .value
                                                                  .data![index];
                                                              return SearchResultCard(
                                                                  index: index,
                                                                  model: model);
                                                            }),
                                                    kheight10,
                                                    controller.nextpageloading
                                                        ? const CircularProgressIndicator
                                                            .adaptive()
                                                        : const SizedBox()
                                                  ],
                                                );
                                              },
                                            ),
                                            kheight50,
                                            kheight50,
                                          ],
                                        ),
                                      )
                                : SizedBox(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        kheight20,
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            const Text(
                                              'Recent Search',
                                              style: TextStyle(
                                                  fontWeight: FontWeight.w700,
                                                  fontSize: 18),
                                            ),
                                            InkWell(
                                              onTap: (() {
                                                storage.remove('searchlist');
                                                controller.searchlist.value =
                                                    storage.read(
                                                            'searchlist') ??
                                                        [];
                                              }),
                                              child: const Text(
                                                'Clear',
                                                style: TextStyle(
                                                    color: kblue,
                                                    fontWeight:
                                                        FontWeight.w500),
                                              ),
                                            )
                                          ],
                                        ),
                                        kheight10,
                                        Wrap(
                                          alignment: WrapAlignment.start,
                                          spacing: 8,
                                          runSpacing: 8,
                                          children: List.generate(
                                              controller
                                                  .searchlist.value.length,
                                              (index) => InkWell(
                                                    onTap: (() {
                                                      controller
                                                              .textEditingController
                                                              .value
                                                              .text =
                                                          controller.searchlist[
                                                              index];
                                                      searchchanged(controller
                                                          .textEditingController
                                                          .value
                                                          .text);
                                                    }),
                                                    child: Column(
                                                      children: [
                                                        Container(
                                                          decoration: BoxDecoration(
                                                              border: Border.all(
                                                                  color: kblack
                                                                      .withOpacity(
                                                                          0.5))),
                                                          child: Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                        .symmetric(
                                                                    horizontal:
                                                                        8.0,
                                                                    vertical:
                                                                        5),
                                                            child: Text(
                                                              controller
                                                                      .searchlist[
                                                                  index],
                                                              style: TextStyle(
                                                                  color: kblack
                                                                      .withOpacity(
                                                                          0.5)),
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  )),
                                        )
                                      ],
                                    ).paddingSymmetric(horizontal: 24),
                                  )
                      ],
                    ))),
            const AnimatedCartView()
          ],
        ),
      ),
    );
  }

  searchchanged(String value) {
    if (value.isEmpty) {
      controller.textEditingController.value.clear();
      controller.searchresults.value = SearchModel();
      return;
    }
    if (value.isNotEmpty && value.length > 1) {
      if (query != controller.textEditingController.value.text) {
        controller.searchLoading.value = true;

        _debouncer.run(() async {
          page = 1;
          await controller.getproducts(
              query: controller.textEditingController.value.text);
          controller.searchLoading.value = false;

          query = controller.textEditingController.value.text.trim();
          Future.delayed(const Duration(milliseconds: 1000), () {
            if (query == controller.textEditingController.value.text &&
                controller.searchresults.value.data!.isNotEmpty &&
                query.isNotEmpty) {
              if (controller.searchlist.contains(query)) {
                controller.searchlist.remove(query);
              }
              controller.searchlist.value.insert(0, query);
              if (controller.searchlist.value.length > 10) {
                controller.searchlist.value.removeLast();
              }
              storage.write('searchlist', controller.searchlist.value);
              controller.searchlist.value = storage.read('searchlist') ?? [];
            }
          });
        });
      }
    } else {
      controller.searchLoading.value = false;
    }
  }
}

class SearchResultCard extends StatelessWidget {
  const SearchResultCard({
    Key? key,
    required this.model,
    required this.index,
  }) : super(key: key);

  final SearchResultItem model;
  final int index;

  @override
  Widget build(BuildContext context) {
    print(model.mrp);
    var discountVal = model.mrp == "0.00"
        ? 0
        : ((100 *
                    (double.parse(
                            model.mrp == null ? '0' : model.mrp.toString()) -
                        double.parse(model.price == null
                            ? '0'
                            : model.price.toString()))) /
                double.parse(model.mrp == null ? '0' : model.mrp.toString()))
            .floorToDouble();
    return ButtonAnimation(
      onpress: (() async {
        await showBarModalBottomSheet(
            barrierColor: kblack.withOpacity(0.2),
            context: Get.context!,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            builder: ((context) {
              return SizedBox(
                height: Get.height * 0.8,
                child: ProductScreen(
                  productId: model.productId!,
                  variantId: model.variantId,
                ),
              );
            }));
      }),
      animationWidget: Container(
        decoration: BoxDecoration(
            border: Border(
                top: index == 0 || index == 1
                    ? const BorderSide(color: Color(0xffE1E1E1), width: 1)
                    : BorderSide.none,
                right: const BorderSide(color: Color(0xffE1E1E1), width: 1),
                bottom: const BorderSide(color: Color(0xffE1E1E1), width: 1))),
        child: ClipRRect(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Stack(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    kheight5,
                    Center(
                      child: CachedNetworkImage(
                              placeholder: ((context, url) =>
                                  Image.asset("assets/images/error-image.png")),
                              errorWidget: (context, url, error) =>
                                  Image.asset("assets/images/error-image.png"),
                              fit: BoxFit.cover,
                              imageUrl: model.images!)
                          .paddingAll(2),
                    ),
                    kheight10,
                    Align(
                      alignment: Alignment.topLeft,
                      child: Text(
                        model.name!.capitalize!,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                        textAlign: TextAlign.start,
                        style: GoogleFonts.dmSans(
                          height: 1,
                          fontSize: 16,
                          color: Colors.black,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    kheight10,
                    Text(
                      getweight(model.weight!),
                      style: GoogleFonts.dmSans(fontSize: 16),
                    ),
                    kheight10,
                    Row(
                      children: [
                        Column(
                          children: [
                            Text(
                              '₹ ${double.parse(model.price ?? "0").round() == 0 ? double.parse(model.mrp!).round() : double.parse(model.price ?? "0").round()}',
                              style: GoogleFonts.dmSans(
                                height: 1,
                                fontSize: 18,
                                color: Colors.black,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            double.parse(model.price ?? "0").round() != 0 &&
                                    double.parse(model.mrp ?? "0").round() != 0
                                ? Text(
                                    '₹ ${double.parse(model.mrp!).round()}',
                                    style: TextStyle(
                                        color: Colors.black.withOpacity(0.5),
                                        decoration: TextDecoration.lineThrough),
                                  )
                                : const SizedBox()
                          ],
                        ),
                        const Spacer(),
                        StatefulBuilder(builder: (context, setState) {
                          // DatabaseHelper.instance.getGroceries();

                          return GetBuilder<CartController>(
                            init: CartController(),
                            builder: (cartController) {
                              return FutureBuilder<int>(
                                  future: DatabaseHelper.instance.getQty(
                                      ShopingCart(
                                          variantID: model.variantId!,
                                          productID: model.productId!)),
                                  builder: (context, snapshot) {
                                    if (snapshot.data == null) {
                                      return SizedBox(
                                        height: 30,
                                        width: 80,
                                        child: Stack(
                                          alignment: Alignment.center,
                                          children: [
                                            CustomAppShimmer(
                                              child: Container(
                                                height: 30,
                                                width: 65,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(2),
                                                  color: kgrey.withOpacity(0.5),
                                                ),
                                              ),
                                            ),
                                            Shimmer.fromColors(
                                                baseColor: Colors.black
                                                    .withOpacity(0.1),
                                                highlightColor: Colors.white,
                                                child: const Text("Add",
                                                    style: TextStyle(
                                                      fontSize: 15,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ))),
                                          ],
                                        ),
                                      );
                                    }
                                    return snapshot.data == 0
                                        ? InkWell(
                                            onTap: (() async {
                                              await DatabaseHelper.instance
                                                  .addUpdate(ShopingCart(
                                                mrp: double.parse(
                                                    model.mrp.toString()),
                                                name: model.name,
                                                qty: 1,
                                                productID: model.productId!,
                                                price: double.parse(
                                                    model.price.toString()),
                                                variantID: model.variantId!,
                                                imageURL: model.images ?? '',
                                                weight: double.parse(
                                                    model.weight.toString()),
                                              ));

                                              print('added to cart');
                                              final eventItem =
                                                  AnalyticsEventItem(
                                                itemId:
                                                    model.productId.toString(),
                                                itemName: model.name,
                                                itemCategory: model.category,
                                                itemVariant: model.variantName,
                                                price: double.parse(
                                                    model.price.toString()),
                                                quantity: 1,
                                              );
                                              await FirebaseService
                                                  .firebaseAnalytics
                                                  .logAddToCart(
                                                      items: [eventItem]);

                                              setState(() {
                                                // update = 1;
                                              });
                                            }),
                                            child: Center(
                                                child: Container(
                                              height: 30,
                                              width: 50,
                                              decoration: BoxDecoration(
                                                  color: kblack,
                                                  borderRadius:
                                                      BorderRadius.circular(2)),
                                              child: Center(
                                                child: Text(
                                                  'Add',
                                                  style: GoogleFonts.dmSans(
                                                      color: kwhite,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      fontSize: 16),
                                                ),
                                              ),
                                            )),
                                          )
                                        : Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              AnimatedContainer(
                                                duration: const Duration(
                                                    milliseconds: 100),
                                                decoration: BoxDecoration(
                                                    color: kblack,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            2)),
                                                height: 30,
                                                width: 75.0,
                                                child: Center(
                                                    child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceAround,
                                                  children: [
                                                    Flexible(
                                                      child: InkWell(
                                                        onTap: () async {
                                                          await DatabaseHelper
                                                              .instance
                                                              .removeUpdate(
                                                                  ShopingCart(
                                                            mrp: double.parse(
                                                                model.mrp
                                                                    .toString()),
                                                            name: model.name,
                                                            qty: 1,
                                                            productID: model
                                                                .productId!,
                                                            price: double.parse(
                                                                model.price
                                                                    .toString()),
                                                            variantID: model
                                                                .variantId!,
                                                            imageURL:
                                                                model.images ??
                                                                    '',
                                                            weight: double
                                                                .parse(model
                                                                    .weight
                                                                    .toString()),
                                                          ));
                                                          setState(() {
                                                            // update = 1;
                                                          });
                                                          print(
                                                              'remove to cart');
                                                        },
                                                        child: const Icon(
                                                          Icons.remove_outlined,
                                                          color: Colors.white,
                                                        ),
                                                      ),
                                                    ),
                                                    Flexible(
                                                      child: Text(
                                                        "${snapshot.data ?? ""}",
                                                        style:
                                                            GoogleFonts.dmSans(
                                                          fontSize: 18,
                                                          fontWeight:
                                                              FontWeight.w700,
                                                          color: Colors.white,
                                                        ),
                                                      ),
                                                    ),
                                                    Flexible(
                                                      child: InkWell(
                                                        onTap: () async {
                                                          await DatabaseHelper
                                                              .instance
                                                              .addUpdate(
                                                                  ShopingCart(
                                                            mrp: double.parse(
                                                                model.mrp
                                                                    .toString()),
                                                            name: model.name,
                                                            qty: 1,
                                                            productID: model
                                                                .productId!,
                                                            price: double.parse(
                                                                model.price
                                                                    .toString()),
                                                            variantID: model
                                                                .variantId!,
                                                            imageURL:
                                                                model.images ??
                                                                    '',
                                                            weight: double
                                                                .parse(model
                                                                    .weight
                                                                    .toString()),
                                                          ));
                                                          print(
                                                              "qty increased");
                                                          setState(() {
                                                            // update = 1;
                                                          });
                                                        },
                                                        child: const Icon(
                                                          Icons.add,
                                                          color: Colors.white,
                                                        ),
                                                      ),
                                                    )
                                                  ],
                                                )),
                                              ),
                                            ],
                                          );
                                  });
                            },
                          );
                        }),
                      ],
                    ),
                    kheight20,
                  ],
                ),
                discountVal == 0
                    ? const SizedBox(
                        width: 32,
                        height: 44,
                      )
                    : Container(
                        child: Align(
                            alignment: Alignment.topLeft,
                            child: SizedBox(
                              width: 32,
                              height: 44,
                              child: Stack(
                                alignment: Alignment.topLeft,
                                children: [
                                  SvgPicture.asset('assets/svg/offertag.svg',
                                      color: const Color(0xffFF5454)),
                                  Center(
                                    child: Text(
                                      "${discountVal.round()}% \n OFF",
                                      style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w700,
                                          letterSpacing: -0.5),
                                      textAlign: TextAlign.center,
                                    ).paddingOnly(bottom: 10),
                                  ),
                                ],
                              ),
                            )),
                      ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class Debouncer {
  final int milliseconds;
  Timer? _timer;

  Debouncer({required this.milliseconds});

  run(VoidCallback action) {
    if (_timer?.isActive ?? false) _timer?.cancel();
    _timer = Timer(Duration(milliseconds: milliseconds), action);
  }
}

// ListView.builder(
//                                     physics: NeverScrollableScrollPhysics(),
//                                     shrinkWrap: true,
//                                     itemCount: controller
//                                         .searchresults.value.data?.length,
//                                     itemBuilder: (context, index) {
//                                       return ListTile(
//                                         onTap: (() {
//                                           controller.searchlist.value
//                                               .insert(0, query);
//                                           if (controller
//                                                   .searchlist.value.length >
//                                               10) {
//                                             controller.searchlist.value
//                                                 .removeLast();
//                                           }
//                                           storage.write('searchlist',
//                                               controller.searchlist.value);
//                                           controller.searchlist.value =
//                                               storage.read('searchlist') ?? [];
//                                           Get.to(() => ProductScreen(
//                                               productId: controller
//                                                   .searchresults
//                                                   .value
//                                                   .data![index]
//                                                   .productId!));
//                                         }),
//                                         dense: true,
//                                         minLeadingWidth: 15,
//                                         leading: const Icon(
//                                           Icons.search_rounded,
//                                           size: 20,
//                                         ),
//                                         title: Text(
//                                           controller.searchresults.value
//                                               .data![index].name
//                                               .toString()
//                                               .capitalize!,
//                                           style: const TextStyle(
//                                               color: kblack, fontSize: 15),
//                                         ),
//                                       );
//                                     },
//                                   )
