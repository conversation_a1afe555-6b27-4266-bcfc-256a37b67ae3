import 'package:Rapsap/view/widgets/commons.dart';

class FilterScreen extends StatelessWidget {
  const FilterScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          backgroundColor: kwhite,
          toolbarHeight: 0,
          elevation: 0,
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(65),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Padding(
                            padding: const EdgeInsets.only(
                                left: 20, right: 10, bottom: 3),
                            child: IconButton(
                                onPressed: () {
                                  Get.back();
                                },
                                icon: SizedBox(
                                  height: 20,
                                  child: const Icon(
                                    Icons.arrow_back,
                                    color: kblue,
                                  ),
                                ))),
                        Text(
                          'Filters',
                          style: const TextStyle(
                              fontSize: 20,
                              color: kblack,
                              fontWeight: FontWeight.w700),
                        ),
                      ],
                    ),
                  ],
                ),
                kheight10,
                Divider(
                  thickness: 4,
                  color: kblack.withOpacity(0.06),
                  height: 0,
                )
              ],
            ),
          )),
      body: Column(
        children: [
          Column(
            children: [],
          ),
          Column(
            children: [],
          )
        ],
      ),
    );
  }
}
