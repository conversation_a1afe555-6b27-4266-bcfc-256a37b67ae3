import 'dart:convert';

import 'package:Rapsap/controllers/home_view_controller.dart';
import 'package:Rapsap/model/SubcategoryModel/sub_category_model/datum.dart';
import 'package:Rapsap/model/category_product_model/category_products_model/datum.dart';
import 'package:Rapsap/view/screens/mapscreen/setlocation.dart';
import 'package:Rapsap/view/screens/searchscreen/search.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:marquee/marquee.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:shimmer/shimmer.dart';
import 'package:staggered_grid_view_flutter/widgets/staggered_grid_view.dart';
import 'package:staggered_grid_view_flutter/widgets/staggered_tile.dart';

import '../../../controllers/cartcontrolller.dart';
import '../../../controllers/categorycontroller.dart';
import '../../../main.dart';
import '../../../model/CategoryModel/data.dart';
import '../../../services/databaseHelper.dart';
import '../../../services/firebaseservices.dart';
import '../../widgets/animationviewcart.dart';
import '../../widgets/commons.dart';
import '../cart_screen/cart_screen.dart';
import '../product_screen/product_screen.dart';

class CategoryScreen extends StatefulWidget {
  CategoryScreen({
    Key? key,
    required this.categoryid,
    required this.categoryname,
    this.subcategoryid,
  }) : super(key: key);
  int? subcategoryid;

  int categoryid;
  String categoryname;

  @override
  State<CategoryScreen> createState() => _CategoryScreenState();
}

class _CategoryScreenState extends State<CategoryScreen>
    with TickerProviderStateMixin {
  late final AnimationController _controller = AnimationController(
    lowerBound: 0.8,
    upperBound: 1,
    reverseDuration: const Duration(milliseconds: 100),
    duration: const Duration(seconds: 2),
    vsync: this,
  )..repeat(reverse: true);
  late final Animation<double> _animation = CurvedAnimation(
    parent: _controller,
    curve: Curves.easeInBack,
  );
  final CategoryController categoryController = Get.find();
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    getdata();
    setState(() {});
    scrollController.addListener(() {
      var nextPageTrigger =
          scrollController.position.maxScrollExtent - Get.height * 0.3;
      if (scrollController.position.pixels > nextPageTrigger &&
          categoryController.nextpageloading == false &&
          categoryController.categorytotal.value >
              categoryController.categoryproductmodel.length) {
        categoryController.nextpageloading = true;
        categoryController.update();

        getproducts();
      }
    });

    // TODO: implement initState
    super.initState();
  }

  @override
  dispose() {
    categoryController.subcategoryloading = true;
    categoryController.selectedindex = 0;
    categoryController.loading = true;
    categoryController.update();
    categoryController.categoryproductmodel.clear();
    categoryController.subcategorymodel.clear();

    scrollController.dispose();
    _controller.dispose();

    super.dispose();
  }

  getdata() async {
    categoryController.name.value = widget.categoryname;

    categoryController.selectedcategoryid = widget.categoryid;
    categoryController.subcategoryloading = true;
    categoryController.selectedindex = 0;
    categoryController.loading = true;
    categoryController.update();
    categoryController.categoryproductmodel.clear();
    categoryController.subcategorymodel.clear();
    categoryController.update();

    categoryController.page = 1;
    await readcacheddata();

    var data = await categoryController.getSubcategoryList(
        categoryId: widget.categoryid);
    if (widget.subcategoryid != null &&
        categoryController.subcategorymodel.isNotEmpty) {
      categoryController.selectedindex = categoryController.subcategorymodel
          .lastIndexWhere(
              (element) => element!.subCategoryId == widget.subcategoryid);
      widget.subcategoryid == null;
    }

    await writecachedata(data);

    final productdata = await categoryController.getProductsByCategory(
        type: "sub_category",
        categoryId: widget.categoryid,
        subcategoryid: data.first!.subCategoryId.toString());

    var dataAsMap = productdata.map((e) => e?.toJson()).toList();
    String jsonString = jsonEncode(dataAsMap);
    print("jsonString $jsonString");

    storage.write(data.first!.subCategoryId.toString(), jsonString);

    // storage.write(data.first!.subCategoryId.toString(),
    //     categoryController.categoryproductmodel);
  }

  readcacheddata() async {
    if (storage.read('${widget.categoryid}') != null) {
      print('not null');
      var result = storage.read('${widget.categoryid}');
      List jsonData = jsonDecode(result);
      print(jsonData);
      if (jsonData.isNotEmpty) {
        await Future.delayed(const Duration(milliseconds: 500), () {
          if (widget.categoryid ==
              jsonData
                  .map((e) => SubCategoryData.fromJson(e))
                  .toList()
                  .obs
                  .first
                  .categoryId) {
            categoryController.subcategorymodel =
                jsonData.map((e) => SubCategoryData.fromJson(e)).toList().obs;

            if (widget.subcategoryid != null &&
                categoryController.subcategorymodel.isNotEmpty) {
              categoryController.selectedindex = categoryController
                  .subcategorymodel
                  .lastIndexWhere((element) =>
                      element!.subCategoryId == widget.subcategoryid);
              widget.subcategoryid == null;
            }
          } else {
            return;
          }
        });
      }
      if (categoryController.subcategorymodel.isNotEmpty) {
        if (storage.read(
                '${categoryController.subcategorymodel[categoryController.selectedindex]!.subCategoryId}') !=
            null) {
          print("produccts not null");
          var presult = storage.read(
              '${categoryController.subcategorymodel[categoryController.selectedindex]!.subCategoryId}');
          List jsonPdata = jsonDecode(presult);
          await Future.delayed(const Duration(milliseconds: 200), () {
            if (jsonPdata.isNotEmpty) {
              print("jsonPdata $jsonPdata");
              if (categoryController
                      .subcategorymodel[categoryController.selectedindex]!
                      .subCategoryId ==
                  jsonPdata
                      .map((e) => CategoryProducts.fromJson(e))
                      .toList()
                      .obs
                      .first
                      .subCategoryId) {
                categoryController.categoryproductmodel = jsonPdata
                    .map((e) => CategoryProducts.fromJson(e))
                    .toList()
                    .obs;
                categoryController.loading = false;
                categoryController.update();

                categoryController.subcategoryloading = false;
                categoryController.update();
              }
            }
          });
        }
      }
    }
  }

  writecachedata(data) {
    var dataAsMap = data.map((e) => e?.toJson()).toList();
    String jsonString = jsonEncode(dataAsMap);

    storage.write(widget.categoryid.toString(), jsonString);
  }

  getproducts() async {
    await categoryController.getProductsByCategory(
        type: "sub_category",
        page: categoryController.page++,
        categoryId: widget.categoryid,
        subcategoryid: categoryController
            .subcategorymodel[categoryController.selectedindex]?.subCategoryId
            .toString());
  }

  @override
  Widget build(BuildContext context) {
    final HomeViewController homeViewController = Get.find();
    return Scaffold(
      backgroundColor: kwhite,
      appBar: AppBar(
        toolbarHeight: 80,
        foregroundColor: kblack,
        elevation: 0,
        automaticallyImplyLeading: false,
        backgroundColor: kwhite,
        leading: InkWell(
          onTap: (() {
            Get.back();
          }),
          child: SvgPicture.asset(
            'assets/svg/iconback.svg',
          ).paddingAll(16),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 30,
              width: 300,
              child: GetBuilder<CategoryController>(
                builder: (_) {
                  return PopupMenuButton(
                      position: PopupMenuPosition.under,
                      constraints:
                          const BoxConstraints.expand(width: 200, height: 300),
                      padding: EdgeInsets.zero,
                      icon: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Flexible(
                              child: Obx(
                            () => categoryController.name.value.length > 17
                                ? Marquee(
                                    blankSpace: 30,
                                    textDirection: TextDirection.ltr,
                                    text: homeViewController.categoryModel
                                            .firstWhere((element) =>
                                                element!.categoryId ==
                                                widget.categoryid)
                                            ?.name ??
                                        "",
                                    velocity: 40,
                                    style: GoogleFonts.dmSans(
                                        fontSize: 20,
                                        fontWeight: FontWeight.w700),
                                  )
                                : Text(
                                    homeViewController.categoryModel
                                            .firstWhere((element) =>
                                                element!.categoryId ==
                                                widget.categoryid)
                                            ?.name ??
                                        "",
                                    style: GoogleFonts.dmSans(
                                        fontSize: 20,
                                        fontWeight: FontWeight.w700)),
                          )),
                          const Padding(
                            padding: EdgeInsets.only(top: 3),
                            child: Icon(Icons.keyboard_arrow_down_sharp),
                          ),
                        ],
                      ),
                      itemBuilder: (ctx) =>
                          List.generate(homeViewController.categoryModel.length,
                              ((index) {
                            return buildPopupMenuItem(
                              homeViewController.categoryModel[index]!,
                              _,
                            );
                          })));
                },
              ),
            ),
            GetBuilder<CategoryController>(
              builder: (_) {
                return Text(
                  '(${homeViewController.categoryModel.firstWhere((element) => element!.categoryId == widget.categoryid)?.totalproducts ?? ""}) Products',
                  style: const TextStyle(fontSize: 12),
                );
              },
            )
          ],
        ),
        actions: [
          GestureDetector(
            child: IconButton(
                    onPressed: () {
                      Get.to(const SearchScreen());
                    },
                    icon: const Icon(CupertinoIcons.search))
                .paddingAll(16),
          )
        ],
      ),
      body: Stack(
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                  width: 80,
                  child: Column(
                    children: [
                      Expanded(child: GetBuilder<CategoryController>(
                        builder: (_) {
                          return _.subcategoryloading
                              ? ListView.builder(
                                  itemCount: 10,
                                  itemBuilder: (context, index) {
                                    return SizedBox(
                                      height: 80,
                                      child: CustomAppShimmer(
                                          child: Container(
                                        child: SvgPicture.asset(
                                          'assets/svg/categorybagactive.svg',
                                          color: kgrey.withOpacity(0.5),
                                        ),
                                      ).paddingSymmetric(horizontal: 15)),
                                    );
                                  })
                              : ListView.builder(
                                  itemCount: _.subcategorymodel.length,
                                  itemBuilder: (context, index) {
                                    final model = _.subcategorymodel[index];

                                    return GestureDetector(
                                      onTap: (() async {
                                        categoryController.page = 0;
                                        _.update();

                                        if (_.selectedindex != index) {
                                          _.loading = true;
                                          _.update();

                                          // categoryController
                                          //     .categoryproductmodel
                                          //     .clear();
                                          _.selectedindex = index;
                                          _.update();
                                          _.loading = true;
                                          _.update();

                                          if (storage.read(
                                                  '${categoryController.subcategorymodel[_.selectedindex]!.subCategoryId}') !=
                                              null) {
                                            _.loading = true;
                                            _.update();
                                            print("produccts not null");
                                            var presult = storage.read(
                                                '${categoryController.subcategorymodel[_.selectedindex]!.subCategoryId}');
                                            List jsonPdata =
                                                jsonDecode(presult);
                                            _.loading = true;
                                            _.update();

                                            if (jsonPdata.isNotEmpty) {
                                              await Future.delayed(
                                                  const Duration(
                                                      milliseconds: 200), () {
                                                print("jsonPdata $jsonPdata");
                                                _.loading = true;
                                                _.update();
                                                if (categoryController
                                                        .subcategorymodel[
                                                            _.selectedindex]!
                                                        .subCategoryId ==
                                                    jsonPdata
                                                        .map((e) =>
                                                            CategoryProducts
                                                                .fromJson(e))
                                                        .toList()
                                                        .obs
                                                        .first
                                                        .subCategoryId) {
                                                  _.loading = true;
                                                  _.update();
                                                  categoryController
                                                          .categoryproductmodel =
                                                      jsonPdata
                                                          .map((e) =>
                                                              CategoryProducts
                                                                  .fromJson(e))
                                                          .toList()
                                                          .obs;

                                                  categoryController.loading =
                                                      false;
                                                  categoryController.update();
                                                }
                                              });
                                            }
                                          }
                                          final productdata =
                                              await categoryController
                                                  .getProductsByCategory(
                                                      type: "sub_category",
                                                      categoryId:
                                                          widget.categoryid,
                                                      subcategoryid: model!
                                                          .subCategoryId
                                                          .toString());

                                          var dataAsMap = productdata
                                              .map((e) => e?.toJson())
                                              .toList();
                                          String jsonString =
                                              jsonEncode(dataAsMap);
                                          print("jsonString $jsonString");

                                          if (categoryController
                                                  .subcategorymodel[
                                                      _.selectedindex]!
                                                  .subCategoryId ==
                                              productdata
                                                  .first!.subCategoryId) {
                                            storage.write(
                                                model.subCategoryId.toString(),
                                                jsonString);
                                          }
                                        }
                                      }),
                                      child: Container(
                                        child: Column(
                                          children: [
                                            SizedBox(
                                              height: 80,
                                              child: Stack(
                                                alignment:
                                                    Alignment.bottomCenter,
                                                children: [
                                                  AnimatedPositioned(
                                                      width: _.selectedindex ==
                                                              index
                                                          ? 65
                                                          : 50.0,
                                                      height:
                                                          _.selectedindex ==
                                                                  index
                                                              ? 70
                                                              : 50,
                                                      top: _.selectedindex ==
                                                              index
                                                          ? 0
                                                          : 15,
                                                      duration: const Duration(
                                                          milliseconds: 500),
                                                      child: _
                                                                  .subcategorymodel[_
                                                                      .selectedindex]
                                                                  ?.subCategoryImage ==
                                                              null
                                                          ? Image.asset(
                                                              'assets/images/subdummy.png')
                                                          : CachedNetworkImage(
                                                              imageUrl: _
                                                                  .subcategorymodel[
                                                                      _.selectedindex]
                                                                  ?.subCategoryImage,
                                                              placeholder:
                                                                  ((context,
                                                                      url) {
                                                                return const SizedBox();
                                                              }),
                                                              errorWidget:
                                                                  ((context,
                                                                      url,
                                                                      error) {
                                                                return Image.asset(
                                                                    'assets/images/subdummy.png');
                                                              }),
                                                            )),
                                                  Container(
                                                    color: kwhite,
                                                    child: Image.asset(
                                                            'assets/images/${_.selectedindex == index ? 'activebag' : 'inactivebag'}.png')
                                                        .paddingSymmetric(
                                                            horizontal: 15),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Flexible(
                                                  child: Text(
                                                    (model?.subCategoryName ??
                                                            "")
                                                        .capitalize!,
                                                    textAlign: TextAlign.center,
                                                    style: GoogleFonts.dmSans(
                                                        color:
                                                            _.selectedindex ==
                                                                    index
                                                                ? kblue
                                                                : Colors.black,
                                                        fontWeight:
                                                            _.selectedindex ==
                                                                    index
                                                                ? FontWeight
                                                                    .w700
                                                                : FontWeight
                                                                    .w600,
                                                        fontSize:
                                                            _.selectedindex ==
                                                                    index
                                                                ? 13
                                                                : 12),
                                                  ),
                                                ),
                                              ],
                                            ).paddingSymmetric(horizontal: 10)
                                          ],
                                        ),
                                      ),
                                    );
                                  });
                        },
                      )),
                    ],
                  )),
              Flexible(
                flex: 3,
                child: Container(
                  color: kwhite,
                  child: GetBuilder<CategoryController>(
                    builder: (_) {
                      return Column(
                        children: [
                          Expanded(
                            child: _.loading
                                ? FadeTransition(
                                    opacity: _animation,
                                    child: const CategoryShimmer(),
                                  )
                                : ListView(
                                    physics: const BouncingScrollPhysics(),
                                    controller: scrollController,
                                    children: [
                                      _.subcategorymodel[_.selectedindex]!
                                                  .bannerimage ==
                                              null
                                          ? const SizedBox()
                                          : CachedNetworkImage(
                                              fadeInDuration: const Duration(
                                                  milliseconds: 100),
                                              imageUrl: _
                                                  .subcategorymodel[
                                                      _.selectedindex]!
                                                  .bannerimage,
                                              placeholder: (context, url) =>
                                                  const SizedBox(),
                                              errorWidget:
                                                  (context, url, error) =>
                                                      const SizedBox(),
                                            ).paddingAll(1),
                                      StaggeredGridView.countBuilder(
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          shrinkWrap: true,
                                          itemCount:
                                              _.categoryproductmodel.length + 1,
                                          crossAxisCount: 2,
                                          staggeredTileBuilder: (index) {
                                            if (index ==
                                                _.categoryproductmodel.length) {
                                              return const StaggeredTile.fit(2);
                                            } else {
                                              return const StaggeredTile.fit(1);
                                            }
                                          },
                                          itemBuilder: (context, index) {
                                            if (index ==
                                                categoryController
                                                    .categoryproductmodel
                                                    .length) {
                                              return _.nextpageloading
                                                  ? const Column(children: [
                                                      kheight50,
                                                      CircularProgressIndicator
                                                          .adaptive(),
                                                      kheight50
                                                    ])
                                                  : const SizedBox(
                                                      height: 100,
                                                    );
                                            } else {
                                              final model = _
                                                  .categoryproductmodel[index]!;

                                              return CategoryCard(model: model);
                                            }
                                          }),
                                    ],
                                  ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              )
            ],
          ),
          const AnimatedCartView()
        ],
      ),
    );
  }

  PopupMenuItem buildPopupMenuItem(
      CategoryItemModel data, CategoryController controller) {
    return PopupMenuItem(
      onTap: () {
        controller.selectedcategoryid == data.categoryId;
        controller.update();
        widget.categoryid = data.categoryId!;
        // widget.bannerimage = data.bannerimage;
        controller.name.value = data.name!;
        widget.categoryname = data.name!;
        getdata();
      },
      padding: EdgeInsets.zero,
      child: Container(
        height: 40,
        decoration: const BoxDecoration(
            // border: Border(
            //     bottom: controller.selectedcategoryid == data.categoryId
            //         ? BorderSide(color: kblue, width: 2)
            //         : BorderSide.none)
            ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              controller.selectedcategoryid == data.categoryId
                  ? SizedBox(
                      width: 15,
                      child: SvgPicture.asset(
                        'assets/svg/cart-bag.svg',
                        color: kblue,
                      )).paddingOnly(right: 10)
                  : const SizedBox(),
              Flexible(
                child: Text(
                  data.name!.capitalize!,
                  overflow: TextOverflow.ellipsis,
                  style: GoogleFonts.dmSans(
                      color: controller.selectedcategoryid == data.categoryId
                          ? kblue
                          : kblack,
                      fontSize: controller.selectedcategoryid == data.categoryId
                          ? 16
                          : 14,
                      fontWeight:
                          controller.selectedcategoryid == data.categoryId
                              ? FontWeight.w700
                              : FontWeight.w500),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CategoryShimmer extends StatelessWidget {
  const CategoryShimmer({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            mainAxisExtent: 250, crossAxisCount: 2),
        itemBuilder: (context, index) {
          return Container(
            decoration: BoxDecoration(
                border: Border.all(color: const Color(0xffE1E1E1), width: 0.4)),
            padding: const EdgeInsets.all(8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomAppShimmer(
                  child: Container(
                    height: 110,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(2),
                      color: kgrey.withOpacity(0.5),
                    ),
                  ),
                ),
                kheight20,
                CustomAppShimmer(
                  child: Container(
                    height: 20,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(2),
                      color: kgrey.withOpacity(0.5),
                    ),
                  ),
                ),
                kheight10,
                CustomAppShimmer(
                  child: Container(
                    height: 10,
                    width: 30,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(2),
                      color: kgrey.withOpacity(0.5),
                    ),
                  ),
                ),
                kheight20,
                Row(
                  children: [
                    Column(
                      children: [
                        CustomAppShimmer(
                          child: Container(
                            height: 10,
                            width: 30,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(2),
                              color: kgrey.withOpacity(0.5),
                            ),
                          ),
                        ),
                        kheight5,
                        CustomAppShimmer(
                          child: Container(
                            height: 20,
                            width: 30,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(2),
                              color: kgrey.withOpacity(0.5),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    SizedBox(
                      height: 30,
                      width: 80,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          CustomAppShimmer(
                            child: Container(
                              height: 30,
                              width: 65,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(2),
                                color: kgrey.withOpacity(0.5),
                              ),
                            ),
                          ),
                          Shimmer.fromColors(
                              baseColor: Colors.black.withOpacity(0.1),
                              highlightColor: Colors.white,
                              child: const Text("Add",
                                  style: TextStyle(
                                    fontSize: 15,
                                    fontWeight: FontWeight.bold,
                                  ))),
                        ],
                      ),
                    ),
                  ],
                )
              ],
            ),
          );
        });
  }
}

class CategoryCard extends StatelessWidget {
  const CategoryCard({
    Key? key,
    required this.model,
  }) : super(key: key);

  final CategoryProducts model;

  @override
  Widget build(BuildContext context) {
    // print(model.mrp);
    var discountVal = model.mrp == "0.00"
        ? 0
        : ((100 *
                    (double.parse(
                            model.mrp == null ? '0' : model.mrp.toString()) -
                        double.parse(model.price == null
                            ? '0'
                            : model.price.toString()))) /
                double.parse(model.mrp == null ? '0' : model.mrp.toString()))
            .floorToDouble();
    return Padding(
      padding: const EdgeInsets.all(0.2),
      child: InkWell(
        onTap: (() async {
          await showBarModalBottomSheet(

              // closeProgressThreshold: Get.height * 0.5,
              barrierColor: kblack.withOpacity(0.2),
              context: Get.context!,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10)),
              builder: ((context) {
                return SizedBox(
                  height: Get.height * 0.8,
                  child: ProductScreen(
                    productId: model.productId!,
                    variantId: model.variantId,
                  ),
                );
              }));
        }),
        child: Container(
          decoration: BoxDecoration(
              border: Border.all(color: const Color(0xffE1E1E1), width: 0.4)),
          child: ClipRRect(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5),
              child: Stack(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      kheight5,
                      Center(
                        child: model.images!.isEmpty
                            ? Image.asset('assets/images/error-image.png')
                            : CachedNetworkImage(
                                    placeholder: (context, url) {
                                      return Image.asset(
                                          'assets/images/error-image.png');
                                    },
                                    errorWidget: (context, url, error) =>
                                        Image.asset(
                                            'assets/images/error-image.png'),
                                    fit: BoxFit.fitWidth,
                                    imageUrl: model.images!.first.url!)
                                .paddingAll(2),
                      ),
                      kheight10,
                      Align(
                        alignment: Alignment.topLeft,
                        child: Text(
                          model.name!.capitalize!,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                          textAlign: TextAlign.start,
                          style: GoogleFonts.dmSans(
                            height: 1,
                            fontSize: 16,
                            color: Colors.black,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      kheight10,
                      Text(
                        getweight(model.weight!),
                        style: GoogleFonts.dmSans(fontSize: 16),
                      ),
                      kheight10,
                      Row(
                        children: [
                          Column(
                            children: [
                              Text(
                                '₹ ${double.parse(model.price ?? "0").round() == 0 ? double.parse(model.mrp!).round() : double.parse(model.price ?? "0").round()}',
                                style: GoogleFonts.dmSans(
                                  height: 1,
                                  fontSize: 18,
                                  color: Colors.black,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              double.parse(model.price ?? "0").round() != 0 &&
                                      double.parse(model.mrp ?? "0").round() !=
                                          0
                                  ? Text(
                                      '₹ ${double.parse(model.mrp!).round()}',
                                      style: TextStyle(
                                          color: Colors.black.withOpacity(0.5),
                                          decoration:
                                              TextDecoration.lineThrough),
                                    )
                                  : const SizedBox()
                            ],
                          ),
                          const Spacer(),
                          StatefulBuilder(builder: (context, setState) {
                            // DatabaseHelper.instance.getGroceries();

                            return GetBuilder<CartController>(
                              init: CartController(),
                              builder: (cartController) {
                                return FutureBuilder<int>(
                                    future: DatabaseHelper.instance.getQty(
                                        ShopingCart(
                                            variantID: model.variantId!,
                                            productID: model.productId!)),
                                    builder: (context, snapshot) {
                                      if (snapshot.data == null) {
                                        return SizedBox(
                                          height: 30,
                                          width: 80,
                                          child: Stack(
                                            alignment: Alignment.center,
                                            children: [
                                              CustomAppShimmer(
                                                child: Container(
                                                  height: 30,
                                                  width: 65,
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            2),
                                                    color:
                                                        kgrey.withOpacity(0.5),
                                                  ),
                                                ),
                                              ),
                                              Shimmer.fromColors(
                                                  baseColor: Colors.black
                                                      .withOpacity(0.1),
                                                  highlightColor: Colors.white,
                                                  child: const Text("Add",
                                                      style: TextStyle(
                                                        fontSize: 15,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ))),
                                            ],
                                          ),
                                        );
                                      }
                                      return snapshot.data == 0
                                          ? InkWell(
                                              onTap: (() async {
                                                await DatabaseHelper.instance
                                                    .addUpdate(ShopingCart(
                                                  mrp: double.parse(
                                                      model.mrp.toString()),
                                                  name: model.name,
                                                  qty: 1,
                                                  productID: model.productId!,
                                                  price: double.parse(
                                                      model.price.toString()),
                                                  variantID: model.variantId!,
                                                  imageURL: model
                                                          .images!.isEmpty
                                                      ? ""
                                                      : model.images!.first.url,
                                                  weight: double.parse(
                                                      model.weight.toString()),
                                                ));

                                                print('added to cart');
                                                final eventItem =
                                                    AnalyticsEventItem(
                                                  itemId: model.productId
                                                      .toString(),
                                                  itemName: model.name,
                                                  itemCategory: model.category,
                                                  itemVariant:
                                                      model.variantName,
                                                  price: double.parse(
                                                      model.price.toString()),
                                                  quantity: 1,
                                                );
                                                await FirebaseService
                                                    .firebaseAnalytics
                                                    .logAddToCart(
                                                        items: [eventItem]);

                                                setState(() {
                                                  // update = 1;
                                                });
                                              }),
                                              child: Center(
                                                  child: Container(
                                                height: 30,
                                                width: 65,
                                                decoration: BoxDecoration(
                                                    color: kblack,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            2)),
                                                child: Center(
                                                  child: Text(
                                                    'Add',
                                                    style: GoogleFonts.dmSans(
                                                        color: kwhite,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        fontSize: 16),
                                                  ),
                                                ),
                                              )),
                                            )
                                          : Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                AnimatedContainer(
                                                  duration: const Duration(
                                                      milliseconds: 100),
                                                  decoration: BoxDecoration(
                                                      color: kblack,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              2)),
                                                  height: 30,
                                                  width: 65,
                                                  child: Center(
                                                      child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceAround,
                                                    children: [
                                                      Flexible(
                                                        child: InkWell(
                                                          onTap: () async {
                                                            await DatabaseHelper
                                                                .instance
                                                                .removeUpdate(
                                                                    ShopingCart(
                                                              mrp: double.parse(
                                                                  model.mrp
                                                                      .toString()),
                                                              name: model.name,
                                                              qty: 1,
                                                              productID: model
                                                                  .productId!,
                                                              price: double
                                                                  .parse(model
                                                                      .price
                                                                      .toString()),
                                                              variantID: model
                                                                  .variantId!,
                                                              imageURL: model
                                                                      .images!
                                                                      .isEmpty
                                                                  ? ""
                                                                  : model
                                                                      .images!
                                                                      .first
                                                                      .url,
                                                              weight: double
                                                                  .parse(model
                                                                      .weight
                                                                      .toString()),
                                                            ));
                                                            setState(() {
                                                              // update = 1;
                                                            });
                                                            print(
                                                                'remove to cart');
                                                          },
                                                          child: const Icon(
                                                            Icons
                                                                .remove_outlined,
                                                            size: 17,
                                                            color: Colors.white,
                                                          ),
                                                        ),
                                                      ),
                                                      Flexible(
                                                        child: Text(
                                                          "${snapshot.data ?? ""}",
                                                          style: GoogleFonts
                                                              .dmSans(
                                                            fontSize: 14,
                                                            fontWeight:
                                                                FontWeight.w700,
                                                            color: Colors.white,
                                                          ),
                                                        ),
                                                      ),
                                                      Flexible(
                                                        child: InkWell(
                                                          onTap: () async {
                                                            await DatabaseHelper
                                                                .instance
                                                                .addUpdate(
                                                                    ShopingCart(
                                                              mrp: double.parse(
                                                                  model.mrp
                                                                      .toString()),
                                                              name: model.name,
                                                              qty: 1,
                                                              productID: model
                                                                  .productId!,
                                                              price: double
                                                                  .parse(model
                                                                      .price
                                                                      .toString()),
                                                              variantID: model
                                                                  .variantId!,
                                                              imageURL: model
                                                                      .images!
                                                                      .isEmpty
                                                                  ? ""
                                                                  : model
                                                                      .images!
                                                                      .first
                                                                      .url,
                                                              weight: double
                                                                  .parse(model
                                                                      .weight
                                                                      .toString()),
                                                            ));
                                                            print(
                                                                "qty increased");
                                                            setState(() {
                                                              // update = 1;
                                                            });
                                                          },
                                                          child: const Icon(
                                                            Icons.add,
                                                            size: 17,
                                                            color: Colors.white,
                                                          ),
                                                        ),
                                                      )
                                                    ],
                                                  )),
                                                ),
                                              ],
                                            );
                                    });
                              },
                            );
                          }),
                          kheight5,
                        ],
                      ),
                      kheight5,
                    ],
                  ),
                  discountVal == 0
                      ? const SizedBox(
                          width: 32,
                          height: 44,
                        )
                      : Container(
                          child: Align(
                              alignment: Alignment.topLeft,
                              child: SizedBox(
                                width: 32,
                                height: 44,
                                child: Stack(
                                  alignment: Alignment.topLeft,
                                  children: [
                                    SvgPicture.asset(
                                      'assets/svg/offertag.svg',
                                      color: const Color(0xffFF5454),
                                    ),
                                    Center(
                                      child: Text(
                                        "${discountVal.round()}% \n OFF",
                                        style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 12,
                                            fontWeight: FontWeight.w700,
                                            letterSpacing: -0.5),
                                        textAlign: TextAlign.center,
                                      ).paddingOnly(bottom: 10),
                                    ),
                                  ],
                                ),
                              )),
                        ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class AnimatedSubCategory extends StatefulWidget {
  final Widget child;

  final double height;

  const AnimatedSubCategory(
      {Key? key, required this.child, required this.height})
      : super(key: key);

  @override
  State<AnimatedSubCategory> createState() => _AnimatedSubCategoryState();
}

class _AnimatedSubCategoryState extends State<AnimatedSubCategory> {
  double height = 0;

  @override
  void initState() {
    // TODO: implement initState

    Future.delayed(const Duration(milliseconds: 10), () {
      setState(() {
        height = widget.height;
      });
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      width: double.infinity,
      decoration:
          BoxDecoration(color: kwhite.withOpacity(0.9), boxShadow: const [
        BoxShadow(
          color: kgrey,
          blurRadius: 5.0,
        )
      ]),
      duration: const Duration(milliseconds: 100),
      height: height,
      child: widget.child,
    );
  }
}
