import 'package:Rapsap/controllers/categorycontroller.dart';
import 'package:Rapsap/controllers/wishlistcontroller.dart';
import 'package:Rapsap/main.dart';
import 'package:Rapsap/model/wishlistmodel/wishlistmodel.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:page_view_dot_indicator/page_view_dot_indicator.dart';
import 'package:Rapsap/controllers/home_view_controller.dart';
import 'package:Rapsap/utils/constants.dart';
import 'package:Rapsap/controllers/cartcontrolller.dart';
import 'package:Rapsap/controllers/product_view_controller.dart';
import 'package:Rapsap/controllers/user_controller.dart';
import 'package:Rapsap/model/productmodel/products/products.dart';

import 'package:Rapsap/services/userservices.dart';

import 'package:shimmer/shimmer.dart';

import '../../../model/category_product_model/category_products_model/datum.dart';
import '../../../services/databaseHelper.dart';
import '../../../services/firebaseservices.dart';
import '../../widgets/animationviewcart.dart';
import '../cart_screen/cart_screen.dart';

class ProductScreen extends StatefulWidget {
  const ProductScreen({Key? key, required this.productId, this.variantId})
      : super(key: key);

  final int productId;
  final int? variantId;

  @override
  State<ProductScreen> createState() => _ProductScreenState();
}

class _ProductScreenState extends State<ProductScreen> {
  final controller = Get.find<ProductViewController>();
  final categorycontroller = Get.find<CategoryController>();
  final cartController = Get.find<CartController>();
  final UserController userController = Get.find<UserController>();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    print("widget.productid=${widget.productId}");
    return FutureBuilder(
        future: controller.getproducts(widget.productId),
        builder: (context, AsyncSnapshot<Products?> snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return SingleChildScrollView(
                child: const ProductScreenShimmer().paddingAll(defaultpadding));
          }
          if (snapshot.data == null) {
            return const Center(
              child: Text('Some thing went Wrong'),
            );
          }

          final productmodel = snapshot.data!.data!;

          print(" productmodel=${productmodel.toJson()}");
          if (productmodel.variants == null) {
            return const Center(
              child: Text('Some thing went Wrong'),
            );
          }
          if (productmodel.variants!.isEmpty) {
            return const Center(
              child: Text('Some thing went Wrong'),
            );
          }

          controller.variant_index.value = widget.variantId == null
              ? 0
              : productmodel.variants!.indexWhere(
                  (element) => element.variantId == widget.variantId);
          if (controller.variant_index.value < 0) {
            return const Center(
              child: Text('Some thing went Wrong'),
            );
          }
          final eventItem = AnalyticsEventItem(
            itemId: productmodel.productId.toString(),
            itemName: productmodel.name,
            itemCategory: productmodel.category,
            itemVariant: productmodel
                .variants?[controller.variant_index.value].variantId
                .toString(),
            price: double.parse(productmodel
                .variants![controller.variant_index.value].price
                .toString()),
          );
          FirebaseService.firebaseAnalytics
              .logViewItem(currency: "INR", items: [eventItem]);
          var discountVal =
              productmodel.variants![controller.variant_index.value].mrp == null
                  ? 0.00
                  : ((100 *
                              (double.parse(productmodel
                                      .variants![controller.variant_index.value]
                                      .mrp!) -
                                  double.parse(productmodel
                                      .variants![controller.variant_index.value]
                                      .price!))) /
                          double.parse(productmodel
                              .variants![controller.variant_index.value].mrp!))
                      .floorToDouble();

          controller.currentdiscount = discountVal;
          final Wishlistcontroller wishlistcontroller = Get.find();

          controller.favourite = wishlistcontroller.wislistItems == null
              ? false
              : wishlistcontroller.wislistItems!.isEmpty
                  ? false
                  : wishlistcontroller.wislistItems!.any((element) =>
                      element.productId == productmodel.productId &&
                      element.variantId ==
                          productmodel.variants!.first.variantId);
          print(controller.favourite);
          print('wishlist ${wishlistcontroller.wislistItems}');

          return Stack(children: [
            SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Obx(
                      () => _productImageCard(
                          productmodel
                              .variants![controller.variant_index.value].images,
                          productmodel.productId,
                          productmodel.variants![controller.variant_index.value]
                              .variantId,
                          productmodel.isFavorite),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Flexible(
                          child: Text(
                            '${productmodel.name.toString().capitalize}',
                            style: GoogleFonts.dmSans(
                              fontSize: 24,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        GetBuilder<ProductViewController>(
                          builder: (controller) {
                            return InkWell(
                                onTap: (() async {
                                  controller.favourite = !controller.favourite;
                                  controller.update();

                                  final findindex = wishlistcontroller
                                      .wislistItems!
                                      .indexWhere((element) =>
                                          element.productId ==
                                              productmodel.productId &&
                                          element.variantId ==
                                              productmodel
                                                  .variants!.first.variantId);
                                  print(findindex);
                                  if (findindex >= 0) {
                                    wishlistcontroller.wislistItems!
                                        .removeAt(findindex);
                                    wishlistcontroller.update();
                                  } else {
                                    if (controller.favourite) {
                                      wishlistcontroller.wislistItems!.add(
                                          WislistItems(
                                              productId: productmodel.productId,
                                              variantId: productmodel
                                                  .variants!.first.variantId));
                                      wishlistcontroller.update();
                                    }
                                  }
                                  await UserService.updateWishlist({
                                    "update_name": "updateWishlist",
                                    "user_id":
                                        userController.userdata.value.data!.id,
                                    "product_id": productmodel.productId,
                                    "variant_id":
                                        productmodel.variants!.first.variantId,
                                  });

                                  wishlistcontroller.getwishlistitems();
                                }),
                                child: !controller.favourite
                                    ? const Icon(Icons.favorite_outline_sharp)
                                    : const Icon(
                                        Icons.favorite,
                                        color: Colors.red,
                                      ));
                          },
                        )
                      ],
                    ).paddingOnly(left: 16, top: 20, right: 16),
                    const SizedBox(height: 11),

                    ListView.builder(
                      physics: const NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      itemCount: productmodel.variants!.length,
                      itemBuilder: (context, index) {
                        final model = productmodel.variants![index];
                        return GestureDetector(
                          onTap: () {
                            controller.variant_index.value = index;
                          },
                          child: Container(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Flexible(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            productmodel.variants![index]
                                                    .variantName ??
                                                productmodel.name ??
                                                "",
                                            style: const TextStyle(
                                                color: Color(0xff626262),
                                                fontWeight: FontWeight.w700,
                                                fontSize: 16),
                                          ),
                                          Container(
                                              child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              Text(
                                                "₹${double.parse(productmodel.variants![index].price.toString()).round()}",
                                                style: GoogleFonts.dmSans(
                                                    fontSize: 17,
                                                    fontWeight: FontWeight.w700,
                                                    color: kblue),
                                              ),
                                              const SizedBox(width: 11),
                                              Text(
                                                productmodel.variants![index]
                                                            .mrp ==
                                                        productmodel
                                                            .variants![index]
                                                            .price
                                                    ? ""
                                                    : "₹${double.parse(productmodel.variants![index].mrp.toString()).round()}",
                                                style: GoogleFonts.dmSans(
                                                  fontSize: 14,
                                                  decoration: TextDecoration
                                                      .lineThrough,
                                                  fontWeight: FontWeight.w400,
                                                  color:
                                                      const Color(0xff556F80),
                                                ),
                                              ),
                                              const SizedBox(width: 11),
                                              controller.currentdiscount > 0
                                                  ? Container(
                                                      height: 20,
                                                      width: 55,
                                                      decoration: BoxDecoration(
                                                        color: const Color(
                                                            0xffFF5454),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(2),
                                                      ),
                                                      child: Center(
                                                        child: Text(
                                                          "${controller.currentdiscount.round()}% OFF",
                                                          style: GoogleFonts
                                                              .dmSans(
                                                            fontSize: 11,
                                                            fontWeight:
                                                                FontWeight.w700,
                                                            color: Colors.white,
                                                          ),
                                                        ),
                                                      ),
                                                    )
                                                  : const SizedBox()
                                            ],
                                          )),
                                        ],
                                      ),
                                    ),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        StatefulBuilder(
                                            builder: (context, setState) {
                                          // DatabaseHelper.instance.getGroceries();

                                          return FutureBuilder<int>(
                                              future: DatabaseHelper.instance
                                                  .getQty(ShopingCart(
                                                      variantID:
                                                          model.variantId!,
                                                      productID: productmodel
                                                          .productId!)),
                                              builder: (context, snapshot) {
                                                return snapshot.data == 0
                                                    ? InkWell(
                                                        onTap: (() async {
                                                          await DatabaseHelper
                                                              .instance
                                                              .addUpdate(
                                                                  ShopingCart(
                                                            mrp: double.parse(
                                                                model.mrp
                                                                    .toString()),
                                                            name: productmodel
                                                                .name,
                                                            qty: 1,
                                                            productID:
                                                                productmodel
                                                                    .productId!,
                                                            price: double.parse(
                                                                model.price
                                                                    .toString()),
                                                            variantID: model
                                                                .variantId!,
                                                            imageURL: model
                                                                    .images!
                                                                    .isEmpty
                                                                ? ""
                                                                : model
                                                                        .images!
                                                                        .first
                                                                        .url ??
                                                                    "",
                                                            weight: double
                                                                .parse(model
                                                                    .weight
                                                                    .toString()),
                                                          ));
                                                          controller
                                                              .variant_index
                                                              .value = index;

                                                          print(
                                                              'added to cart');
                                                          final eventItem =
                                                              AnalyticsEventItem(
                                                            itemId: productmodel
                                                                .productId
                                                                .toString(),
                                                            itemName:
                                                                productmodel
                                                                    .name,
                                                            itemCategory:
                                                                productmodel
                                                                    .category,
                                                            itemVariant: model
                                                                .variantName,
                                                            price: double.parse(
                                                                model.price
                                                                    .toString()),
                                                            quantity: 1,
                                                          );
                                                          await FirebaseService
                                                              .firebaseAnalytics
                                                              .logAddToCart(
                                                                  items: [
                                                                eventItem
                                                              ]);

                                                          setState(() {
                                                            // update = 1;
                                                          });
                                                        }),
                                                        child: Center(
                                                            child: Container(
                                                          height: 30,
                                                          width: 75,
                                                          decoration: BoxDecoration(
                                                              color: kblack,
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          2)),
                                                          child: Center(
                                                            child: Text(
                                                              'Add',
                                                              style: GoogleFonts.dmSans(
                                                                  color: kwhite,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500,
                                                                  fontSize: 16),
                                                            ),
                                                          ),
                                                        )),
                                                      )
                                                    : Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .center,
                                                        children: [
                                                          AnimatedContainer(
                                                            duration:
                                                                const Duration(
                                                                    milliseconds:
                                                                        100),
                                                            decoration: BoxDecoration(
                                                                color: kblack,
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            2)),
                                                            height: 30,
                                                            width: 75.0,
                                                            child: Center(
                                                                child: Row(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .spaceAround,
                                                              children: [
                                                                Flexible(
                                                                  child:
                                                                      InkWell(
                                                                    onTap:
                                                                        () async {
                                                                      await DatabaseHelper
                                                                          .instance
                                                                          .removeUpdate(
                                                                              ShopingCart(
                                                                        mrp: double.parse(model
                                                                            .mrp
                                                                            .toString()),
                                                                        name: productmodel
                                                                            .name,
                                                                        qty: 1,
                                                                        productID:
                                                                            productmodel.productId!,
                                                                        price: double.parse(model
                                                                            .price
                                                                            .toString()),
                                                                        variantID:
                                                                            model.variantId!,
                                                                        imageURL: model.images!.isEmpty
                                                                            ? ""
                                                                            : model.images!.first.url ??
                                                                                '',
                                                                        weight: double.parse(model
                                                                            .weight
                                                                            .toString()),
                                                                      ));
                                                                      setState(
                                                                          () {
                                                                        // update = 1;
                                                                      });
                                                                      print(
                                                                          'remove to cart');
                                                                    },
                                                                    child:
                                                                        const Icon(
                                                                      Icons
                                                                          .remove_outlined,
                                                                      color: Colors
                                                                          .white,
                                                                    ),
                                                                  ),
                                                                ),
                                                                GetBuilder<
                                                                    CartController>(
                                                                  init:
                                                                      CartController(),
                                                                  initState:
                                                                      (_) {},
                                                                  builder:
                                                                      (cartController) {
                                                                    return Flexible(
                                                                      child:
                                                                          Text(
                                                                        "${snapshot.data ?? ""}",
                                                                        style: GoogleFonts
                                                                            .dmSans(
                                                                          fontSize:
                                                                              18,
                                                                          fontWeight:
                                                                              FontWeight.w700,
                                                                          color:
                                                                              Colors.white,
                                                                        ),
                                                                      ),
                                                                    );
                                                                  },
                                                                ),
                                                                Flexible(
                                                                  child:
                                                                      InkWell(
                                                                    onTap:
                                                                        () async {
                                                                      await DatabaseHelper
                                                                          .instance
                                                                          .addUpdate(
                                                                              ShopingCart(
                                                                        mrp: double.parse(model
                                                                            .mrp
                                                                            .toString()),
                                                                        name: productmodel
                                                                            .name,
                                                                        qty: 1,
                                                                        productID:
                                                                            productmodel.productId!,
                                                                        price: double.parse(model
                                                                            .price
                                                                            .toString()),
                                                                        variantID:
                                                                            model.variantId!,
                                                                        imageURL: model.images!.isEmpty
                                                                            ? ""
                                                                            : model.images!.first.url ??
                                                                                '',
                                                                        weight: double.parse(model
                                                                            .weight
                                                                            .toString()),
                                                                      ));
                                                                      print(
                                                                          "qty increased");
                                                                      controller
                                                                          .variant_index
                                                                          .value = index;
                                                                      setState(
                                                                          () {
                                                                        // update = 1;
                                                                      });
                                                                    },
                                                                    child:
                                                                        const Icon(
                                                                      Icons.add,
                                                                      color: Colors
                                                                          .white,
                                                                    ),
                                                                  ),
                                                                )
                                                              ],
                                                            )),
                                                          ),
                                                        ],
                                                      );
                                              });
                                        }),
                                      ],
                                    ),
                                  ],
                                ).paddingSymmetric(
                                    horizontal: defaultpadding, vertical: 15),
                                const Divider(
                                  height: 0,
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),

                    // const SizedBox(height: 20),
                    // Text(
                    //   "Available Offers",
                    //   style: GoogleFonts.inter(
                    //     fontSize: 16,
                    //     fontWeight: FontWeight.w500,
                    //     color: const Color(0xff18394C),
                    //   ),
                    // ).paddingSymmetric(horizontal: 24),
                    // const SizedBox(height: 24),
                    // Row(
                    //   children: [
                    //     getSvgIcon("assets/svg/tag.svg"),
                    //     const SizedBox(width: 10),
                    //     Expanded(
                    //       child: RichText(
                    //         text: TextSpan(
                    //           text: '10% off with Kotak Credit Cards.',
                    //           style: GoogleFonts.dmSans(
                    //               color: const Color(0xff556F80)),
                    //           children: [
                    //             TextSpan(
                    //               text: " View details",
                    //               style: GoogleFonts.inter(
                    //                 fontSize: 14,
                    //                 fontWeight: FontWeight.w500,
                    //                 color: kblue,
                    //               ),
                    //             ),
                    //           ],
                    //         ),
                    //       ),
                    //     )
                    //   ],
                    // ).paddingSymmetric(horizontal: 24),
                    // const SizedBox(height: 24),
                    // Row(
                    //   crossAxisAlignment: CrossAxisAlignment.start,
                    //   children: [
                    //     getSvgIcon("assets/svg/tag.svg"),
                    //     const SizedBox(width: 10),
                    //     Expanded(
                    //       child: RichText(
                    //         text: TextSpan(
                    //           text:
                    //               '20% off with ICICI Corporate Credit Cards.',
                    //           style: GoogleFonts.dmSans(
                    //               color: const Color(0xff556F80)),
                    //           children: [
                    //             TextSpan(
                    //               text: " \nView details",
                    //               style: GoogleFonts.inter(
                    //                 fontSize: 14,
                    //                 fontWeight: FontWeight.w500,
                    //                 color: kblue,
                    //               ),
                    //             ),
                    //           ],
                    //         ),
                    //       ),
                    //     )
                    //   ],
                    // ).paddingSymmetric(horizontal: 24),
                    // const SizedBox(height: 24),
                    // _dividerWidget(),
                    // const SizedBox(height: 24),
                    productmodel.description == null
                        ? const SizedBox()
                        : ListTile(
                            contentPadding: EdgeInsets.zero,
                            title: Row(
                              children: [
                                Text(
                                  "Product Detail",
                                  style: GoogleFonts.dmSans(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w500,
                                    color: const Color(0xff18394C),
                                  ),
                                ),
                              ],
                            ),
                          ).paddingSymmetric(horizontal: defaultpadding),

                    Align(
                      alignment: Alignment.bottomLeft,
                      child: Text(
                        productmodel.description ?? "",
                        style: GoogleFonts.dmSans(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: const Color(0xff556F80),
                        ),
                      ),
                    ).paddingSymmetric(horizontal: defaultpadding),
                    const SizedBox(height: 18),

                    // const SizedBox(height: 24),
                    const SizedBox(height: 24),

                    const SizedBox(height: 38),
                    Text(
                      "Similar Products",
                      style: GoogleFonts.dmSans(
                        fontSize: 24,
                        fontWeight: FontWeight.w700,
                        color: const Color(0xff181725),
                      ),
                    ).paddingSymmetric(horizontal: defaultpadding),
                    const SizedBox(height: 20),
                    FutureBuilder<List<CategoryProducts?>>(
                        future: categorycontroller.getProductsByCategory(
                            type: "similar",
                            categoryId: productmodel.categoryId!,
                            subcategoryid:
                                productmodel.subCategoryId.toString()),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return SizedBox(
                              height: 200,
                              child: ListView.builder(
                                      shrinkWrap: true,
                                      scrollDirection: Axis.horizontal,
                                      itemCount: 4,
                                      itemBuilder:
                                          (BuildContext context, int index) {
                                        return Shimmer.fromColors(
                                            baseColor: Colors.grey[200]!,
                                            highlightColor: Colors.grey[100]!,
                                            child: Container(
                                              height: 200,
                                              width: 140,
                                              color: Colors.grey,
                                            ).paddingAll(5));
                                      })
                                  .paddingSymmetric(horizontal: defaultpadding),
                            );
                          }
                          if (snapshot.data == null) {
                            return const SizedBox();
                          }
                          print(snapshot.data!
                              .where((element) =>
                                  element!.productId != productmodel.productId)
                              .length);
                          return SizedBox(
                              height: 300,
                              child: ListView.builder(
                                shrinkWrap: true,
                                physics: const BouncingScrollPhysics(),
                                scrollDirection: Axis.horizontal,
                                itemCount: snapshot.data!
                                    .where((element) =>
                                        element!.productId !=
                                        productmodel.productId)
                                    .length,
                                itemBuilder: (BuildContext context, int index) {
                                  final model = snapshot.data!
                                      .where((element) =>
                                          element!.productId !=
                                          productmodel.productId)
                                      .toList();
                                  return _similarProductCard(
                                      index, model[index]);
                                },
                              ));
                        }),
                    const SizedBox(height: 40),
                  ],
                )),
            const AnimatedCartView()
          ]);
        });
  }

  _similarProductCard(index, CategoryProducts? model) {
    // print("model=${model!.toJson()}"),;
    var discountVal = model!.mrp == null
        ? 0
        : ((100 *
                    (double.parse(
                            model.mrp == null ? '0' : model.mrp.toString()) -
                        double.parse(model.price == null
                            ? '0'
                            : model.price.toString()))) /
                double.parse(model.mrp == null ? '0' : model.mrp.toString()))
            .floorToDouble();
    final HomeViewController homeViewController =
        Get.find<HomeViewController>();
    final UserController userController = Get.find<UserController>();
    final Wishlistcontroller wishlistcontroller = Get.find();

    return Row(
      children: [
        index == 0
            ? const SizedBox(
                width: 16,
              )
            : const SizedBox(),
        Column(children: [
          SizedBox(
            width: 140,
            height: 250,
            child: Stack(
              children: [
                InkWell(
                  onTap: () {
                    Get.close(1);
                    showBarModalBottomSheet(
                        barrierColor: kblack.withOpacity(0.2),
                        context: Get.context!,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10)),
                        builder: ((context) {
                          return SizedBox(
                            height: Get.height * 0.8,
                            child: ProductScreen(
                              productId: model.productId!,
                              variantId: model.variantId,
                            ),
                          );
                        }));
                  },
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(2),
                    child: Card(
                      shadowColor: kgrey,
                      elevation: 3,
                      surfaceTintColor: kgrey.withOpacity(0.5),
                      child: SizedBox(
                        height: 232,
                        child: Stack(
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(10),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  const SizedBox(height: 2),
                                  SizedBox(
                                    height: 110,
                                    child: model.images!.isEmpty
                                        ? Image.asset(
                                            "assets/images/error-image.png")
                                        : CachedNetworkImage(
                                            imageUrl:
                                                "${model.images!.first.url}",
                                            fit: BoxFit.cover,
                                            errorWidget: (context, error,
                                                    stackTrace) =>
                                                Image.asset(
                                                    "assets/images/error-image.png"),
                                          ),
                                  ),
                                  const SizedBox(height: 5),
                                  SizedBox(
                                    height: 36,
                                    child: Align(
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        "${model.name.toString().capitalizeFirst}",
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 2,
                                        textAlign: TextAlign.start,
                                        style: GoogleFonts.dmSans(
                                          height: 1,
                                          fontSize: 16,
                                          color: Colors.black,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ),
                                  Align(
                                    alignment: Alignment.topLeft,
                                    child: Text(
                                      model.weight != null
                                          ? getweight(model.weight.toString())
                                          : "",
                                      style: GoogleFonts.dmSans(
                                          fontSize: 12,
                                          color: Colors.grey,
                                          fontWeight: FontWeight.w400,
                                          letterSpacing: -0.1),
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  Align(
                                    alignment: Alignment.topLeft,
                                    child: Row(
                                      children: [
                                        FittedBox(
                                          child: Text(
                                            "₹${double.parse((model.price ?? '').toString()).round()}",
                                            style: GoogleFonts.dmSans(
                                              fontSize: 16,
                                              color: kblack,
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 5),
                                        Text(
                                          model.price == model.mrp ||
                                                  model.mrp == "0.00"
                                              ? ""
                                              : "₹${double.parse((model.mrp ?? '').toString()).round()}",
                                          style: GoogleFonts.dmSans(
                                            fontSize: 12,
                                            color: kblack.withOpacity(0.5),
                                            fontWeight: FontWeight.w400,
                                            decoration:
                                                TextDecoration.lineThrough,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                ],
                              ),
                            ),
                            discountVal == 0
                                ? const SizedBox(
                                    width: 32,
                                    height: 44,
                                  )
                                : Container(
                                    child: Align(
                                        alignment: Alignment.topRight,
                                        child: SizedBox(
                                          width: 32,
                                          height: 44,
                                          child: Stack(
                                            alignment: Alignment.topRight,
                                            children: [
                                              SvgPicture.asset(
                                                'assets/svg/offertag.svg',
                                                color: const Color(0xffFF5454),
                                              ),
                                              Center(
                                                child: Text(
                                                  "${discountVal.round()}% \n OFF",
                                                  style: const TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 12,
                                                      fontWeight:
                                                          FontWeight.w700,
                                                      letterSpacing: -0.5),
                                                  textAlign: TextAlign.center,
                                                ).paddingOnly(bottom: 10),
                                              ),
                                            ],
                                          ),
                                        )),
                                  ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: StatefulBuilder(builder: (context, setState) {
                    // DatabaseHelper.instance.getGroceries();

                    return FutureBuilder<int>(
                        future: DatabaseHelper.instance.getQty(ShopingCart(
                            variantID: model.variantId!,
                            productID: model.productId!)),
                        builder: (context, snapshot) {
                          return snapshot.data == 0
                              ? InkWell(
                                  onTap: (() async {
                                    await DatabaseHelper.instance
                                        .addUpdate(ShopingCart(
                                      mrp: double.parse(model.mrp.toString()),
                                      name: model.name,
                                      qty: 1,
                                      productID: model.productId!,
                                      price:
                                          double.parse(model.price.toString()),
                                      variantID: model.variantId!,
                                      imageURL: model.images!.isEmpty
                                          ? ""
                                          : model.images!.first.url ?? '',
                                      weight:
                                          double.parse(model.weight.toString()),
                                    ));

                                    print('added to cart');
                                    final eventItem = AnalyticsEventItem(
                                      itemId: model.productId.toString(),
                                      itemName: model.name,
                                      itemCategory: model.category,
                                      itemVariant: model.variantName,
                                      price:
                                          double.parse(model.price.toString()),
                                      quantity: 1,
                                    );
                                    await FirebaseService.firebaseAnalytics
                                        .logAddToCart(items: [eventItem]);

                                    setState(() {
                                      // update = 1;
                                    });
                                  }),
                                  child: Center(
                                      child: Container(
                                          height: 34,
                                          width: 34,
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                              color: kblack,
                                              borderRadius:
                                                  BorderRadius.circular(2)),
                                          child: const Icon(
                                            Icons.add,
                                            color: kwhite,
                                          ))),
                                )
                              : Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    AnimatedContainer(
                                      duration:
                                          const Duration(milliseconds: 100),
                                      decoration: BoxDecoration(
                                          color: kblack,
                                          borderRadius:
                                              BorderRadius.circular(2)),
                                      height: 34,
                                      width: 102.0,
                                      child: Center(
                                          child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceAround,
                                        children: [
                                          Flexible(
                                            child: InkWell(
                                              onTap: () async {
                                                await DatabaseHelper.instance
                                                    .removeUpdate(ShopingCart(
                                                  mrp: double.parse(
                                                      model.mrp.toString()),
                                                  name: model.name,
                                                  qty: 1,
                                                  productID: model.productId!,
                                                  price: double.parse(
                                                      model.price.toString()),
                                                  variantID: model.variantId!,
                                                  imageURL:
                                                      model.images!.isEmpty
                                                          ? ""
                                                          : model.images!.first
                                                                  .url ??
                                                              '',
                                                  weight: double.parse(
                                                      model.weight.toString()),
                                                ));
                                                setState(() {
                                                  // update = 1;
                                                });
                                                print('remove to cart');
                                              },
                                              child: const Icon(
                                                Icons.remove_outlined,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ),
                                          GetBuilder<CartController>(
                                            init: CartController(),
                                            initState: (_) {},
                                            builder: (cartController) {
                                              return Flexible(
                                                child: Text(
                                                  "${snapshot.data ?? ""}",
                                                  style: GoogleFonts.dmSans(
                                                    fontSize: 18,
                                                    fontWeight: FontWeight.w700,
                                                    color: Colors.white,
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                          Flexible(
                                            child: InkWell(
                                              onTap: () async {
                                                await DatabaseHelper.instance
                                                    .addUpdate(ShopingCart(
                                                  mrp: double.parse(
                                                      model.mrp.toString()),
                                                  name: model.price,
                                                  qty: 1,
                                                  productID: model.productId!,
                                                  price: double.parse(
                                                      model.price.toString()),
                                                  variantID: model.variantId!,
                                                  imageURL:
                                                      model.images!.isEmpty
                                                          ? ""
                                                          : model.images!.first
                                                                  .url ??
                                                              "",
                                                  weight: double.parse(
                                                      model.weight.toString()),
                                                ));
                                                print("qty increased");
                                                setState(() {
                                                  // update = 1;
                                                });
                                              },
                                              child: const Icon(
                                                Icons.add,
                                                color: Colors.white,
                                              ),
                                            ),
                                          )
                                        ],
                                      )),
                                    ),
                                  ],
                                );
                        });
                  }),
                ),
              ],
            ),
          ),
        ]),
      ],
    );
  }

  Container _productImageCard(images, productid, variantid, fav) {
    return Container(
      height: Get.height * 0.35 + 30,
      color: kwhite,
      child: Column(
        children: [
          kheight20,
          GetBuilder<ProductViewController>(builder: (controller) {
            return CarouselSlider.builder(
              itemCount: images!.isEmpty ? 1 : images!.length,
              itemBuilder:
                  (BuildContext context, int itemIndex, int pageViewIndex) {
                return images!.isEmpty
                    ? Image.asset("assets/images/error-image.png")
                    : CachedNetworkImage(
                        fit: BoxFit.cover,
                        imageUrl: images![itemIndex].url.toString(),
                        placeholder: (context, url) =>
                            const Center(child: CircularProgressIndicator()),
                        errorWidget: (context, url, error) =>
                            Image.asset("assets/images/error-image.png"),
                      );
              },
              options: CarouselOptions(
                scrollPhysics: images!.length == 1
                    ? const NeverScrollableScrollPhysics()
                    : const ScrollPhysics(),
                initialPage: controller.carouselIndex,
                onPageChanged: (index, reason) {
                  controller.carouselIndex = index;
                  controller.update();
                },
                height: Get.height * 0.35,
                viewportFraction: 1,
                padEnds: true,
                clipBehavior: Clip.antiAlias,
                autoPlay: false,
              ),
            );
          }).paddingSymmetric(horizontal: defaultpadding),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const SizedBox(
                width: 10,
                height: 10,
              ),
              GetBuilder<ProductViewController>(builder: (controller) {
                return SizedBox(
                  width: 100,
                  child: images!.length == 1 || images!.length == 0
                      ? const SizedBox()
                      : PageViewDotIndicator(
                          unselectedSize: const Size(7, 6),
                          size: const Size(8, 7),
                          currentItem: controller.carouselIndex,
                          count: images!.length,
                          unselectedColor: const Color(0xff556f8080),
                          selectedColor: kblue,
                        ),
                ).paddingSymmetric(horizontal: defaultpadding);
              }),
            ],
          ).paddingSymmetric(horizontal: defaultpadding),
        ],
      ),
    );
  }

  Widget getSvgIcon(String icon) => SvgPicture.asset(
        icon,
        semanticsLabel: 'Acme Logo',
      );
}

showProductScreenSheet({required productId, int? variantId}) async {
  await showBarModalBottomSheet(
      barrierColor: kblack.withOpacity(0.2),
      context: Get.context!,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      builder: ((context) {
        return SizedBox(
          height: Get.height * 0.8,
          child: ProductScreen(
            productId: productId!,
            variantId: variantId,
          ),
        );
      }));
}

class ProductScreenShimmer extends StatelessWidget {
  const ProductScreenShimmer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[200]!,
      highlightColor: Colors.grey[100]!,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 40,
            width: double.infinity,
            color: Colors.grey,
          ),
          kheight10,
          Container(
            height: 260,
            width: double.infinity,
            color: Colors.grey,
          ),
          kheight20,
          Container(
            height: 50,
            width: Get.height * 0.25,
            color: Colors.grey,
          ),
          kheight20,
          Container(
            height: 20,
            width: Get.height * 0.1,
            color: Colors.grey,
          ),
          kheight30,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Container(
                  height: 50,
                  color: Colors.grey,
                ),
              ),
              kwidth10,
              Expanded(
                child: Container(
                  height: 50,
                  color: Colors.grey,
                ),
              )
            ],
          ),
          kheight50,
          const Divider(
            thickness: 2,
            color: Colors.grey,
          ),
          kheight30,
          Container(
            height: 20,
            width: 50,
            color: Colors.grey,
          ),
          kheight10,
          Container(
            height: 100,
            width: double.infinity,
            color: Colors.grey,
          )
        ],
      ),
    );
  }
}
