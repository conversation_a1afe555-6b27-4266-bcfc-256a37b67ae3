import 'package:Rapsap/view/widgets/buttonanimation.dart';
import 'package:flutter/material.dart';
import 'package:loading_indicator/loading_indicator.dart';

import '../../../../utils/constants.dart';

class LoginOptionButton extends StatelessWidget {
  const LoginOptionButton(
      {Key? key,
      required this.text,
      required this.onpress,
      required this.textcolor,
      required this.icon,
      required this.backgroundcolor})
      : super(key: key);
  final VoidCallback onpress;
  final String text;
  final Widget icon;
  final Color textcolor;
  final Color backgroundcolor;
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onpress,
      style: ElevatedButton.styleFrom(
        minimumSize: const Size(double.infinity, 56),
        backgroundColor: backgroundcolor,
        side: BorderSide(
          width: 2.0,
          color: backgroundcolor != kwhite ? const Color(0xffD44C3C) : kblack,
        ),
      ),
      child: Row(
        children: [
          icon,
          Expanded(
            child: Align(
              alignment: Alignment.center,
              child: Text(
                text,
                style: TextStyle(
                    color: textcolor,
                    fontWeight: FontWeight.w700,
                    fontSize: 18),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class SubmitButton extends StatelessWidget {
  const SubmitButton(
      {Key? key,
      this.loading = false,
      this.side,
      required this.text,
      required this.onpress,
      this.txtcolor = kwhite,
      this.height = 56,
      this.bgcolor = kblack,
      this.boldness = FontWeight.w700,
      this.textsize = 18})
      : super(key: key);
  final String text;
  final bool loading;

  final double height;
  final VoidCallback? onpress;
  final Color bgcolor;
  final Color txtcolor;
  final BoxBorder? side;
  final double textsize;
  final FontWeight boldness;

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: onpress == null ? 0.5 : 1,
      child: ButtonAnimation(
        onpress: loading ? null : onpress,
        animationWidget: Container(
          decoration: BoxDecoration(
            border: side,
            borderRadius: BorderRadius.circular(2),
            color: onpress == null ? Colors.black45 : bgcolor,
          ),
          height: height,
          child: loading
              ? const SizedBox(
                  height: 20,
                  child: LoadingIndicator(
                    colors: [kblue, kblack],
                    indicatorType: Indicator.cubeTransition,
                  ),
                )
              : Center(
                  child: Text(text,
                      style: TextStyle(
                          color: txtcolor,
                          fontSize: textsize,
                          fontWeight: boldness)),
                ),
        ),
      ),
    );
  }
}
