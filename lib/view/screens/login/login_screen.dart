import 'dart:developer';
import 'dart:io';

import 'package:Rapsap/view/screens/AddressPage/addaddresspage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:Rapsap/view/screens/mapscreen/mappage.dart';
import 'package:Rapsap/view/screens/root_page/root_page.dart';
import 'package:Rapsap/view/widgets/custom.dart';
import 'package:Rapsap/view/widgets/loadingscreen.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../main.dart';
import '../../../services/firebaseservices.dart';
import 'widgets/button.dart';

import 'package:Rapsap/view/widgets/commons.dart';
import 'package:carousel_slider/carousel_slider.dart';

import 'package:pinput/pinput.dart';

import '../../../../controllers/tomercontroller.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  static get vsync => null;
  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final CarouselController controller = CarouselController();

  final UserController _usrController = Get.find<UserController>();

  @override
  void initState() {
    FirebaseService.dynamicLinks.onLink.listen((dynamicLinkData) {
      String payload = dynamicLinkData.link.toString();
      print("Hi $payload");
      if (_usrController.phoneController.value.text.length == 10 &&
          _usrController.currentpin ==
              payload.split('/')[payload.split('/').length - 2]) {
        if (mounted) {
          Get.to(() => const LoadingScreen(text: "Auto Verifying"));

          _usrController.verifyOtp({
            'mobile': _usrController.phoneController.value.text,
            'otp': payload.split('/')[payload.split('/').length - 2],
          });
        }
      } else {
        customToast(message: 'Invalid link');
      }
      // if (payload.split('/').last == "verify") {
      //   pinController.text = payload.split('/')[payload.split('/').length - 2];
      // }
    });
    // TODO: implement initState
    super.initState();
  }

  final pinController = TextEditingController();
  final FocusNode otpCtrlFocusNode = FocusNode();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    final height = Get.mediaQuery.size.height;
    final width = context.width;
    bool visibily = false;
    bool loading = false;

    _usrController.numberSelected.value = true;
    final PinTheme defaultPinTheme = PinTheme(
        width: context.width * 0.1,
        height: context.width * 0.1,
        margin: EdgeInsets.only(right: context.width * 0.03),
        textStyle: const TextStyle(
            fontSize: 20,
            color: Color.fromRGBO(30, 60, 87, 1),
            fontWeight: FontWeight.w600),
        decoration: BoxDecoration(
          color: kwhite,
          borderRadius: BorderRadius.circular(4),
        ));

    return Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: kblack,
        body: Stack(
          // mainAxisAlignment: MainAxisAlignment.center,
          // mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: height * 0.3,
              width: width,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(loginhead),
                  fit: BoxFit.cover,
                ),
              ),
              child: Center(
                child: SvgPicture.asset(loginheadtitle),
              ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: ClipRect(
                child: CarouselSlider(
                        items: [
                      Stack(
                        children: [
                          SizedBox(
                              child: SvgPicture.asset(
                            'assets/svg/mainbag.svg',
                            fit: BoxFit.fill,
                          )).paddingSymmetric(horizontal: 8),
                          SingleChildScrollView(
                            child: Container(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  kheight20,
                                  AnimatedContainer(
                                      height: MediaQuery.of(context)
                                                  .viewInsets
                                                  .bottom ==
                                              0.0
                                          ? 90
                                          : 50,
                                      duration:
                                          const Duration(milliseconds: 200),
                                      child: Center(
                                        child: SizedBox(
                                            width: 90,
                                            child: SvgPicture.asset(
                                              'assets/svg/hangerbag.svg',
                                            )).paddingAll(5),
                                      )),
                                  const Text(
                                    'Welcome',
                                    style: TextStyle(
                                        height: 2,
                                        color: kblack,
                                        fontWeight: FontWeight.w700,
                                        fontSize: 26),
                                  ),
                                  const Text(
                                    "Enter your whatsapp number and get started ",
                                    style: TextStyle(
                                        color: Color(0xffB8B8B8),
                                        fontWeight: FontWeight.w400,
                                        fontSize: 14),
                                  ),
                                  kheight30,
                                  Obx(() => Form(
                                        key: _formKey,
                                        child: TextFormField(
                                            maxLength: 10,
                                            maxLengthEnforcement:
                                                MaxLengthEnforcement.enforced,
                                            onChanged: ((value) {
                                              _usrController.update();
                                              if (_usrController.phoneController
                                                      .value.text.length >=
                                                  10) {
                                                _usrController.sendOtp({
                                                  "mobile": _usrController
                                                      .phoneController
                                                      .value
                                                      .text
                                                });
                                                controller.animateToPage(1);
                                                otpCtrlFocusNode.requestFocus();
                                              }
                                            }),
                                            onTap: () {
                                              _usrController.loading = false;
                                              _usrController.update();
                                              pinController.clear();

                                              if (_usrController.phoneController
                                                  .value.text.isEmpty) {
                                                if (_usrController
                                                    .numberSelected.value) {
                                                  _usrController
                                                      .getmobilenumber(
                                                          controller,
                                                          otpCtrlFocusNode);
                                                }
                                              }
                                            },
                                            controller: _usrController
                                                .phoneController.value,
                                            keyboardType: TextInputType.number,
                                            inputFormatters: <
                                                TextInputFormatter>[
                                              FilteringTextInputFormatter.allow(
                                                  RegExp(r'[0-9]')),
                                            ],
                                            decoration: InputDecoration(
                                                filled: true,
                                                fillColor: kwhite,
                                                counterText: '',
                                                focusedBorder:
                                                    const OutlineInputBorder(
                                                        borderSide: BorderSide(
                                                            color: kblack,
                                                            width: 2.0),
                                                        borderRadius:
                                                            BorderRadius.zero),
                                                enabledBorder:
                                                    const OutlineInputBorder(
                                                        borderSide:
                                                            BorderSide.none,
                                                        borderRadius:
                                                            BorderRadius.zero),
                                                hintText: 'Phone Number',
                                                hintStyle: const TextStyle(
                                                    color: kgrey,
                                                    fontWeight:
                                                        FontWeight.w500),
                                                prefix: const Text(
                                                  '+91 ',
                                                  style:
                                                      TextStyle(color: kblack),
                                                ),
                                                prefixIcon: Padding(
                                                  padding:
                                                      const EdgeInsets.all(12),
                                                  child: SvgPicture.asset(
                                                    "assets/svg/WhatsApp-Icon.svg",
                                                    color: kblack,
                                                    height: 10,
                                                    width: 10,
                                                  ),
                                                ))),
                                      )),
                                  kheight20,
                                  GetBuilder<UserController>(
                                    builder: (usrController) {
                                      return SubmitButton(
                                        onpress: usrController
                                                    .phoneController.value.text
                                                    .trim()
                                                    .isNotEmpty &&
                                                usrController.phoneController
                                                        .value.text.length ==
                                                    10
                                            ? () {
                                                usrController.loading = false;
                                                usrController.update();
                                                if (usrController
                                                        .phoneController
                                                        .value
                                                        .text
                                                        .trim()
                                                        .isNotEmpty &&
                                                    usrController
                                                            .phoneController
                                                            .value
                                                            .text
                                                            .length ==
                                                        10) {
                                                  FocusManager
                                                      .instance.primaryFocus!
                                                      .unfocus();
                                                  controller.animateToPage(1);
                                                  pinController.clear();
                                                  otpCtrlFocusNode
                                                      .requestFocus();

                                                  usrController.sendOtp({
                                                    "mobile": usrController
                                                        .phoneController
                                                        .value
                                                        .text
                                                  });
                                                } else {
                                                  FocusManager
                                                      .instance.primaryFocus!
                                                      .unfocus();
                                                }
                                              }
                                            : null,
                                        text: 'Get OTP',
                                      );
                                    },
                                  ),
                                  kheight20,
                                  Platform.isAndroid
                                      ? Column(
                                          children: [
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Flexible(
                                                  flex: 2,
                                                  child: SvgPicture.asset(
                                                    'assets/svg/Horizontalsepleft.svg',
                                                  ),
                                                ),
                                                const Flexible(
                                                    child: Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      horizontal: 6.0),
                                                  child: Text(
                                                    'OR',
                                                    style: TextStyle(
                                                        color:
                                                            Color(0xffC5C8CD)),
                                                  ),
                                                )),
                                                Flexible(
                                                  flex: 2,
                                                  child: SvgPicture.asset(
                                                      'assets/svg/Horizontalsepright.svg'),
                                                )
                                              ],
                                            ),
                                            kheight20,
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                InkWell(
                                                  onTap: (() async {
                                                    await Authentication
                                                        .signInWithGoogle(
                                                            context: context);
                                                  }),
                                                  child: CircleAvatar(
                                                    radius: 30,
                                                    backgroundColor: kwhite,
                                                    child: SvgPicture.asset(
                                                            "assets/svg/google.svg")
                                                        .paddingAll(8),
                                                  ),
                                                )
                                                // InkWell(
                                                //   onTap: (() {
                                                //     Get.to(() => RegisterScreen(
                                                //           type: "signup",
                                                //         ));
                                                //   }),
                                                //   child: CircleAvatar(
                                                //     radius: 30,
                                                //     backgroundColor: kwhite,
                                                //     child: SvgPicture.asset(
                                                //         "assets/svg/email.svg"),
                                                //   ),
                                                // )
                                              ],
                                            ),
                                            kheight20,
                                          ],
                                        )
                                      : const SizedBox(),
                                  Center(
                                    child: TextButton(
                                      onPressed: () {
                                        if (kDebugMode) {
                                          print(
                                              storage.read('locationaddress'));
                                        }
                                        storage.read('locationaddress') == null
                                            ? Get.to(() => MapPage())
                                            : Get.to(() => const RootPage());
                                        // processSkipExplore();
                                      },
                                      child: const Text(
                                        'Skip Login & Explore',
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600,
                                          color: kblack,
                                        ),
                                      ),
                                    ),
                                  ),
                                  kheight20,
                                  Center(
                                    child: RichText(
                                      textAlign: TextAlign.center,
                                      text: TextSpan(
                                        text:
                                            'By continuing, you will agree to the\n',
                                        style: const TextStyle(
                                            color: Color(0xff333333),
                                            fontWeight: FontWeight.w400,
                                            height: 1.3),
                                        children: <TextSpan>[
                                          TextSpan(
                                              text: 'Terms and Conditions ',
                                              recognizer: TapGestureRecognizer()
                                                ..onTap = () async {
                                                  if (!await launchUrl(Uri.parse(
                                                      'https://rapsap.com/terms.html'))) {
                                                    throw "Could not launch https://rapsap.com/terms.html";
                                                  }
                                                },
                                              style: const TextStyle(
                                                  fontWeight: FontWeight.w500,
                                                  color: Color.fromRGBO(
                                                      80, 116, 243, 1))),
                                          const TextSpan(
                                              text: '&',
                                              style: TextStyle(
                                                  color: Color.fromRGBO(
                                                      51, 51, 51, 0.8))),
                                          TextSpan(
                                              text: ' Privacy Policy',
                                              recognizer: TapGestureRecognizer()
                                                ..onTap = () async {
                                                  if (!await launchUrl(Uri.parse(
                                                      'https://rapsap.com/terms.html'))) {
                                                    throw "Could not launch https://https://rapsap.com/privacy.html";
                                                  }
                                                },
                                              style: const TextStyle(
                                                  fontWeight: FontWeight.w500,
                                                  color: Color.fromRGBO(
                                                      80, 116, 243, 1))),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ).paddingOnly(
                                left: 24,
                                right: 24,
                              ),
                              // color: Colors.red,
                            ),
                          ),
                        ],
                      ),
                      WillPopScope(
                        onWillPop: () async {
                          controller.animateToPage(0);
                          return false;
                        },
                        child: SizedBox(
                          child: Stack(
                            children: [
                              SizedBox(
                                  child: SvgPicture.asset(
                                'assets/svg/mainbag.svg',
                                fit: BoxFit.fill,
                              )).paddingSymmetric(horizontal: 6),
                              SingleChildScrollView(
                                child: Container(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      kheight30,
                                      AnimatedContainer(
                                          alignment: Alignment.topCenter,
                                          height: MediaQuery.of(context)
                                                      .viewInsets
                                                      .bottom >
                                                  0.0
                                              ? 50
                                              : 90,
                                          duration:
                                              const Duration(milliseconds: 100),
                                          child: Center(
                                              child: SizedBox(
                                                  width: 90,
                                                  child: SvgPicture.asset(
                                                    'assets/svg/hangerbag.svg',
                                                  )))),
                                      hgap(25),
                                      Column(
                                        children: [
                                          Center(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                const Text(
                                                    'Enter Verification Code',
                                                    style: TextStyle(
                                                        fontSize: 26.0,
                                                        color: Colors.black,
                                                        fontWeight:
                                                            FontWeight.bold)),
                                                kheight5,
                                                Row(
                                                  children: [
                                                    const FittedBox(
                                                      child: Text(
                                                        'OTP send to:  ',
                                                        style: TextStyle(
                                                            color: Color(
                                                                0xffB8B8B8),
                                                            fontWeight:
                                                                FontWeight.w400,
                                                            fontSize: 14),
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      height: 15,
                                                      width: 15,
                                                      child: SvgPicture.asset(
                                                        'assets/svg/WhatsApp-Icon.svg',
                                                        color: kblack,
                                                      ),
                                                    ),
                                                    Obx(() => FittedBox(
                                                          child: Text(
                                                              ' +91 ${_usrController.phoneController.value.text}',
                                                              style: const TextStyle(
                                                                  fontSize:
                                                                      14.0,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold)),
                                                        ))
                                                  ],
                                                ),
                                                kheight20,
                                                kheight5,
                                                Center(
                                                  child: Pinput(
                                                      inputFormatters: [
                                                        FilteringTextInputFormatter
                                                            .digitsOnly
                                                      ],
                                                      autofocus: true,
                                                      length: 6,
                                                      errorPinTheme: PinTheme(
                                                          width: context.width *
                                                              0.1,
                                                          height: context.width *
                                                              0.1,
                                                          margin: EdgeInsets.only(
                                                              right: context.width *
                                                                  0.03),
                                                          textStyle: const TextStyle(
                                                              fontSize: 20,
                                                              color: Color.fromRGBO(
                                                                  30, 60, 87, 1),
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600),
                                                          decoration:
                                                              BoxDecoration(
                                                            color: kwhite,
                                                            border: Border.all(
                                                                color:
                                                                    Colors.red),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        4),
                                                          )),
                                                      focusedPinTheme: PinTheme(
                                                          width: context.width *
                                                              0.1,
                                                          height: context.width *
                                                              0.1,
                                                          margin: EdgeInsets.only(
                                                              right: context
                                                                      .width *
                                                                  0.03),
                                                          textStyle: const TextStyle(
                                                              fontSize: 20,
                                                              color: Color.fromRGBO(30, 60, 87, 1),
                                                              fontWeight: FontWeight.w600),
                                                          decoration: BoxDecoration(
                                                            color: kwhite,
                                                            border:
                                                                Border.all(),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        4),
                                                          )),
                                                      submittedPinTheme: PinTheme(width: context.width * 0.1, height: context.width * 0.1, margin: EdgeInsets.only(right: context.width * 0.03), textStyle: const TextStyle(fontSize: 20, color: Color.fromRGBO(30, 60, 87, 1), fontWeight: FontWeight.w600), decoration: BoxDecoration(color: kwhite, border: Border.all(), borderRadius: BorderRadius.circular(4))),
                                                      keyboardType: TextInputType.number,
                                                      controller: pinController,
                                                      defaultPinTheme: defaultPinTheme,
                                                      pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
                                                      textInputAction: TextInputAction.next,
                                                      showCursor: true,
                                                      onChanged: ((value) {
                                                        otpCtrlFocusNode
                                                            .requestFocus();
                                                      }),
                                                      focusNode: otpCtrlFocusNode,
                                                      validator: (s) {
                                                        if (s !=
                                                                _usrController
                                                                    .currentpin &&
                                                            s != "101010") {
                                                          return "Invalid OTP! ";
                                                        }
                                                        return null;
                                                      },
                                                      onCompleted: (value) {
                                                        SystemChannels.textInput
                                                            .invokeMethod(
                                                                'TextInput.hide');

                                                        _usrController
                                                            .verifyOtp({
                                                          'mobile': _usrController
                                                              .phoneController
                                                              .value
                                                              .text,
                                                          'otp':
                                                              pinController.text
                                                        });
                                                      }),
                                                ),
                                                GetBuilder<CountDownTimerState>(
                                                  init: CountDownTimerState(),
                                                  initState: (_) {},
                                                  builder: (_) {
                                                    return Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        _.sCount > 0
                                                            ? Text(
                                                                'Resend in ${_.sCount} ')
                                                            : TextButton(
                                                                onPressed: () {
                                                                  _usrController
                                                                      .sendOtp({
                                                                    "mobile": _usrController
                                                                        .phoneController
                                                                        .value
                                                                        .text
                                                                  });
                                                                  _.sCount = 30;
                                                                  _.stateTimerStart();
                                                                  _.update();
                                                                },
                                                                child:
                                                                    const Text(
                                                                  'Resend OTP',
                                                                  style: TextStyle(
                                                                      color:
                                                                          kblue,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold),
                                                                )),
                                                        // const Spacer(),
                                                        TextButton(
                                                            onPressed: () {
                                                              FocusManager
                                                                  .instance
                                                                  .primaryFocus!
                                                                  .unfocus();
                                                              controller
                                                                  .previousPage(
                                                                      duration:
                                                                          const Duration(
                                                                        milliseconds:
                                                                            500,
                                                                      ),
                                                                      curve: Curves
                                                                          .easeInOut);

                                                              // Get.offAll(() => LoginScreen());
                                                            },
                                                            child: const Text(
                                                                'Change Phone Number',
                                                                style: TextStyle(
                                                                    color: Color(
                                                                        0xFF556F80),
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold)))
                                                      ],
                                                    );
                                                  },
                                                ),
                                                kheight20,
                                                GetBuilder<UserController>(
                                                  builder: (_) {
                                                    return _.loading
                                                        ? const SizedBox(
                                                            height: 56,
                                                            width:
                                                                double.infinity,
                                                            child:
                                                                ElevatedButton(
                                                              onPressed: null,
                                                              child: SizedBox(
                                                                height: 20,
                                                                child:
                                                                    LoadingIndicator(
                                                                  colors: [
                                                                    kblue,
                                                                    kblack
                                                                  ],
                                                                  indicatorType:
                                                                      Indicator
                                                                          .cubeTransition,
                                                                ),
                                                              ),
                                                            ),
                                                          )
                                                        : SubmitButton(
                                                            text: 'Continue',
                                                            onpress: _.loading
                                                                ? null
                                                                : () {
                                                                    _usrController
                                                                        .verifyOtp({
                                                                      'mobile': _usrController
                                                                          .phoneController
                                                                          .value
                                                                          .text,
                                                                      'otp': pinController
                                                                          .text
                                                                    });
                                                                  });
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      )
                                    ],
                                  ).paddingOnly(
                                    left: 24,
                                    right: 24,
                                  ),

                                  // color: Colors.red,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                        carouselController: controller,
                        options: CarouselOptions(
                          height: 600,
                          viewportFraction: 1,
                          initialPage: 0,
                          enableInfiniteScroll: true,
                          reverse: false,
                          autoPlay: false,
                          enlargeCenterPage: true,
                          scrollPhysics: const NeverScrollableScrollPhysics(),
                          scrollDirection: Axis.horizontal,
                        ))
                    .paddingOnly(
                        bottom: MediaQuery.of(context).viewInsets.bottom + 10),
              ),
            )
          ],
        ));
  }
}

class Authentication {
  static Future<User?> signInWithGoogle({required BuildContext context}) async {
    FirebaseAuth auth = FirebaseAuth.instance;
    User? guser;
    final usercontroller = Get.find<UserController>();

    // final GoogleSignIn googleSignIn = GoogleSignIn();
    GoogleSignIn googleSignIn = GoogleSignIn();
    print("object");

    googleSignIn.signOut();
    if (auth.currentUser != null) {
      auth.signOut();
      googleSignIn.signOut();
    }

    final GoogleSignInAccount? googleSignInAccount =
        await googleSignIn.signIn();

    if (googleSignInAccount != null) {
      final GoogleSignInAuthentication googleSignInAuthentication =
          await googleSignInAccount.authentication;

      print("idtoken ${googleSignInAuthentication.idToken}");
      final AuthCredential credential = GoogleAuthProvider.credential(
        accessToken: googleSignInAuthentication.accessToken,
        idToken: googleSignInAuthentication.idToken,
      );

      if (kDebugMode) {
        print("credential $credential");
      }

      try {
        Get.to(() => const LoadingScreen(text: 'Signing in'));

        final UserCredential userCredential =
            await auth.signInWithCredential(credential);
        if (kDebugMode) {
          print("userCredential.user ${userCredential.user}");
        }

        guser = userCredential.user;
        var token = await guser!.getIdToken();
        log("token $token");

        var params = {
          'id_token': token,
        };

        usercontroller.googleSigninVerify(params);
      } on FirebaseAuthException catch (e) {
        if (e.code == 'account-exists-with-different-credential') {
          print("error:: ${e.code}");
          // handle the error here
        } else if (e.code == 'invalid-credential') {
          print("error:: ${e.code}");
          // handle the error here
        } else {
          print("error:: ${e.message}");
        }
      } catch (e) {
        print('error');
        // handle the error here
      }
    }

    return guser;
  }
}
