import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:Rapsap/view/screens/login/register_screen.dart';
import 'package:Rapsap/view/screens/login/widgets/button.dart';
import 'package:Rapsap/view/widgets/keyboardhider.dart';

import '../../../utils/constants.dart';

class ForgotPasswordScreen extends StatelessWidget {
  ForgotPasswordScreen({Key? key}) : super(key: key);
  final TextEditingController _emailController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Forgot Password',
          style: TextStyle(color: kblack, fontWeight: FontWeight.bold),
        ),
        foregroundColor: kblue,
        backgroundColor: Get.theme.scaffoldBackgroundColor,
      ),
      body: KeyboardHider(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              kheight20,
              const Text(
                'Enter your email and we’ll send a link on your \nemail to reset your password.',
                style: TextStyle(
                    color: Color(0xFF556F80),
                    fontSize: 16,
                    fontWeight: FontWeight.w400),
              ),
              kheight20,
              InputTextField(
                  label: 'Email',
                  hint: 'Enter email',
                  txtcontroller: _emailController),
              kheight30,
              SubmitButton(text: 'Send Link', onpress: () {})
            ],
          ).paddingAll(20),
        ),
      ),
    );
  }
}
