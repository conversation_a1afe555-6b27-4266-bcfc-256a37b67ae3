import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:Rapsap/view/widgets/keyboardhider.dart';

import '../../../utils/constants.dart';
import 'forgot_email.dart';
import 'widgets/button.dart';

class ResetPasswordScreen extends StatelessWidget {
  const ResetPasswordScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Reset Password',
          style: TextStyle(color: kblack, fontWeight: FontWeight.bold),
        ),
        foregroundColor: kblue,
        backgroundColor: Get.theme.scaffoldBackgroundColor,
      ),
      body: KeyboardHider(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              kheight20,
              const Text(
                'We have sent a password reset link to your email ID.\n\nClick on the ‘Reset Password’ link sent to your email ID and create a new password.',
                style: TextStyle(
                    color: Color(0xFF556F80),
                    fontSize: 16,
                    fontWeight: FontWeight.w400),
              ),
              kheight30,
              SubmitButton(text: 'Open Email App', onpress: () {}),
              kheight10,
              TextButton(
                  onPressed: () {
                    Get.to(() => ForgotPasswordScreen());
                  },
                  child: const Text(
                    'Resend Link',
                    style: TextStyle(
                      color: kblue,
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                    ),
                  )),
            ],
          ).paddingAll(20),
        ),
      ),
    );
  }
}
