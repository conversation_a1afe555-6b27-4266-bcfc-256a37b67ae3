import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Rapsap/utils/constants.dart';
import 'package:Rapsap/controllers/user_controller.dart';
import 'package:Rapsap/view/screens/login/email_login.dart';
import 'package:Rapsap/view/screens/login/widgets/button.dart';
import 'package:Rapsap/view/widgets/keyboardhider.dart';
import 'package:Rapsap/view/widgets/loadingscreen.dart';

class RegisterScreen extends GetView<UserController> {
  RegisterScreen(
      {Key? key, this.type, this.phonenumber, this.newuser, this.userid})
      : super(key: key);
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final String? type;
  final bool? newuser;
  final int? userid;
  final String? phonenumber;
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _pwdController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          type == 'signup' ? 'Sign up' : 'Personal Details',
          style: const TextStyle(color: kblack, fontWeight: FontWeight.bold),
        ),
        foregroundColor: kblue,
        backgroundColor: Get.theme.scaffoldBackgroundColor,
      ),
      body: KeyboardHider(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    kheight10,
                    InputTextField(
                        hint: 'Enter Name',
                        label: 'Name',
                        txtcontroller: _nameController),
                    kheight10,
                    InputTextField(
                        hint: 'Enter Email',
                        label: 'Email',
                        txtcontroller: _emailController),
                    kheight10,
                    Obx(() => InputTextField(
                        hint: 'Enter Password',
                        label: 'Password',
                        obscure: controller.showpassword.value,
                        txtcontroller: _pwdController)),
                    kheight30,
                    SubmitButton(
                      text: 'Continue',
                      onpress: () {
                        if (_formKey.currentState!.validate()) {
                          Get.to(() => const LoadingScreen(text: 'Signing Up'));
                          print('userid: $userid');
                          if (userid != null) {
                            controller.editProfile({
                              "user_id": userid.toString(),
                              "email": _emailController.text,
                              "password": _pwdController.text,
                              "first_name": _nameController.text,
                            });
                          } else {
                            controller.registerUser({
                              "email": _emailController.text,
                              "password": _pwdController.text,
                              "name": _nameController.text
                            });
                          }
                        }
                      },
                    ),
                    kheight20,
                    GestureDetector(
                      onTap: () {
                        Get.to(() => EmailLogin());
                      },
                      child: RichText(
                        text: const TextSpan(
                          text: 'Already have an account? ',
                          style: TextStyle(
                              fontWeight: FontWeight.w500, color: kblack),
                          children: <TextSpan>[
                            TextSpan(
                                text: 'Login',
                                style: TextStyle(
                                    fontWeight: FontWeight.bold, color: kblue)),
                          ],
                        ),
                      ),
                    )
                  ],
                )),
          ),
        ),
      ),
    );
  }
}

class InputTextField extends GetView<UserController> {
  const InputTextField({
    Key? key,
    this.obscure = false,
    required this.label,
    required this.hint,
    required this.txtcontroller,
  }) : super(key: key);
  final String label;
  final String hint;
  final bool obscure;
  final TextEditingController txtcontroller;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        kheight5,
        TextFormField(
          controller: txtcontroller,
          obscureText: obscure,

          decoration: InputDecoration(
              hintStyle: const TextStyle(color: kblack),
              // isDense: true,
              focusedBorder: const OutlineInputBorder(
                  borderSide: BorderSide(color: kblack, width: 2.0),
                  borderRadius: BorderRadius.zero),
              enabledBorder: const OutlineInputBorder(
                  borderSide: BorderSide(color: kgrey, width: 2.0),
                  borderRadius: BorderRadius.zero),
              border: const OutlineInputBorder(
                  borderSide: BorderSide(color: kgrey, width: 2.0),
                  borderRadius: BorderRadius.zero),
              hintText: hint,
              suffix: label == 'Password'
                  ? GestureDetector(
                      onTap: () {
                        controller.showpassword.value =
                            !controller.showpassword.value;
                      },
                      child: Obx(() => Text(
                            controller.showpassword.value ? 'Show' : 'Hide',
                            style: TextStyle(
                                color: kblue, fontWeight: FontWeight.w500),
                          )))
                  : null),
          // The validator receives the text that the user has entered.
          validator: (value) {
            return GetUtils.isBlank(value)!
                ? ' cannot be empty'
                : label == 'Email'
                    ? GetUtils.isEmail(value!)
                        ? null
                        : 'Enter valid email'
                    : label == 'Name'
                        ? value!.length < 4
                            ? 'Enter valid name'
                            : null
                        : label == 'Password'
                            ? value!.length < 6
                                ? 'Min 6 characters required'
                                : null
                            : null;
          },
        ),
        kheight10,
      ],
    );
  }
}
