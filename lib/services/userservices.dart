import 'dart:convert';
import 'dart:developer';

import 'package:Rapsap/controllers/accountscontroller.dart';
import 'package:Rapsap/view/screens/AddressPage/addaddresspage.dart';
import 'package:Rapsap/view/screens/launchingsoonpage.dart';
import 'package:flutter/foundation.dart';
import 'package:Rapsap/view/screens/root_page/root_page.dart';

import '../model/rewards/rewardsmode.dart';
import '../view/widgets/commons.dart';
import '../main.dart';
import '../model/user_model/usermodel.dart';
import 'package:http/http.dart' as http;

import '../model/wishlistmodel/wishlistmodel.dart';

class UserService {
  static var client = http.Client();

  //save token

  static Future saveDeviceToken(params) async {
    // final storage = new FlutterSecureStorage();
    // String? jwt = await storage.read(key: "jwt");
    // SharedPreferences share = await SharedPreferences.getInstance();
    // String jwt = (share.getString('jwt') ?? '');
    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';
    String url = '${baseURL}api/v1/mobile/saveDeviceToken';
    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    print('req payload:: ${json.encode(params)}');

    var token;
    try {
      final response = await client.post(Uri.parse(url),
          body: json.encode(params), headers: headers);
      // const jsonResponse = json.decode(response);
      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final jsonMap = json.decode(jsonRes);
        print('here-${response.body}');
        token = jsonMap;
      }
      return token;
    } catch (e) {
      print(e);
    }
    return token;
  }

  static Future checkServiceable(params) async {
    print('body ${json.encode(params)}');
    // final storage = new FlutterSecureStorage();
    // String? jwt = await storage.read(key: "jwt");
    // SharedPreferences share = await SharedPreferences.getInstance();
    // String jwt = (share.getString('jwt') ?? '');
    GetStorage storage = GetStorage();
    String jwt = storage.read('jwt') ?? '';

    String url = '${baseURL}api/v1/mobile/checkServiceable';
    // String url = baseURL + 'api/v1/mobile/checklatlongRadius';
    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    // print('body1$requestBody');

    var pincode;
    try {
      final response = await client.post(Uri.parse(url),
          body: json.encode(params), headers: headers);
      // const jsonResponse = json.decode(response);
      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final jsonMap = json.decode(jsonRes);
        print('here-$jsonMap');
        pincode = jsonMap;
      }
      return pincode;
    } catch (e) {
      print(e);
    }
    return pincode;
  }

  //Email Login
  static Future<UserData?> emailLogin(params) async {
    String url = '${baseURL}api/v1/mobile/loginUser';

    Map<String, String> body = {
      'username': params['email'],
      'password': params['password']
    };
    UserData? userModel;
    try {
      var response = await client.post(Uri.parse(url), body: body);
      print("response + ${response.body.toString()}");
      if (response.statusCode == 200) {
        var jsonRes = response.body;
        var jsonMap = json.decode(jsonRes);
        print("json $jsonMap");

        // storage.write('mobile', jsonMap['data']['mobile'].toString());
        // storage.write('jwt', jsonMap['data']['token']);

        userModel = UserData.fromJson(jsonMap);

        if (userModel == null) {
          Get.back();
        }
      }

      return userModel;
    } catch (e) {
      print(e);
    }

    return userModel;
  }

  static Future updateProfile(params) async {
    print('sahal update');
    if (kDebugMode) {
      print(params);
    }

    String url = '${baseURL}api/v1/mobile/editProfile';

    // print('body1$requestBody');

    var userModel;
    try {
      log("jwt = ${storage.read('JWT')}");
      final jwt = storage.read('JWT');
      print(jwt);

      Map<String, String> headers = {
        'Authorization': 'Bearer $jwt',
      };

      final response =
          await client.post(Uri.parse(url), body: params, headers: headers);
      if (kDebugMode) {
        print(response.body.toString());
      }
      if (kDebugMode) {
        print(response.statusCode);
      }
      // const jsonResponse = json.decode(response);
      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final jsonMap = json.decode(jsonRes);
        if (kDebugMode) {
          print('here-$jsonMap');
        }
        userModel = jsonMap;
      }
      return userModel;
    } catch (e) {
      print(e);
    }
    return userModel;
  }

  static Future updateWishlist(params) async {
    String url = '${baseURL}api/v1/ecomm/updateWishlist';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    print(json.encode(params));
    try {
      var response = await client.post(Uri.parse(url),
          body: json.encode(params), headers: headers);

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);

      if (jsonResponse['success'] == false) {
        Get.to(() => const RootPage());
      } else {
        // var parsedJSON = jsonResponse['data'];
        print('here--- $jsonResponse');

        // orderList = addressFromJson(json.encode(jsonResponse['data']));
        return jsonResponse;
      }
    } catch (e, stackTrace) {
      print('Error in $url =>:: $e  \nStackTrace: $stackTrace');
      return null;
    }
  }

  static Future claimDailyRewards(payload) async {
    //  print(payload['offer_id']);
    // SharedPreferences share = await SharedPreferences.getInstance();
    // String jwt = (share.getString('jwt') ?? '');
    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';
    String url = '${baseURL}api/v1/mobile/claimOfferByUser';
    Map<String, String> headers = {'Authorization': 'Bearer $jwt'};
    UserController usertCTRL = Get.find();
    // print('object ${usertCTRL.currentUser.value.data.id}');

    Map<String, String> body = {
      'user_id': usertCTRL.userdata.value.data!.id.toString(),
      'offer_id': payload['offer_id'].toString()
    };

    var currentReward;
    try {
      final response =
          await client.post(Uri.parse(url), body: body, headers: headers);
      // const jsonResponse = json.decode(response);
      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final jsonMap = json.decode(jsonRes);
        // print('here-$jsonMap');
        currentReward = jsonMap;
      }
      return currentReward;
    } catch (e) {
      print(e);
    }
    return currentReward;
  }

  static Future getUserConfig(type) async {
    String url = '${baseURL}api/v1/mobile/userConfig';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };
    print("type $type");

    final UserController userController = Get.find();
    try {
      var response = await client.post(Uri.parse(url),
          body:
              json.encode({'user_id': userController.userdata.value.data!.id}),
          headers: headers);

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);
      print("type $type");

      if (jsonResponse['success'] == true) {
        var data = jsonResponse['data'];

        if (data['is_delete'] == 1) {
          return true;
        } else {
          log("Hello ${data.toString()}");
          if (data['tester'] != 1) {
            log(type);
            if (type == "home") return Get.to(() => const LaunchingSoon());
          }

          return false;
        }
        // Get.to(() => RootPage());
      } else {
        if (type == "home") {
          if (userController.userdata.value.data != null) {
            cleardata();
            return Get.offAll(() => const LoginScreen());
          }
        }
      }
    } catch (e, stackTrace) {
      print('Error in $url =>:: $e  \nStackTrace: $stackTrace');
      return null;
    }
  }

  static Future deleteAccount(params) async {
    String url = '${baseURL}api/v1/mobile/requestAccountDelete';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    print(json.encode(params));
    try {
      var response = await client.post(Uri.parse(url),
          body: json.encode(params), headers: headers);

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);

      if (jsonResponse['success'] == false) {
        // Get.to(() => root

        print('here--- $jsonResponse');

        // orderList = addressFromJson(json.encode(jsonResponse['data']));
        return jsonResponse;
      }
    } catch (e, stackTrace) {
      print('Error in $url =>:: $e  \nStackTrace: $stackTrace');
      return null;
    }
  }

  static Future<List<Rewards>> getUserRewards1() async {
    String url = '${baseURL}api/v1/mobile/getUserRewards';
    // SharedPreferences share = await SharedPreferences.getInstance();
    // String jwt = (share.getString('jwt') ?? '');
    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';
    UserData userData = UserData.fromJson(storage.read('userdata'));
    String userID = userData.data?.id.toString() ?? "";
    // User user;
    // print(userToJson(user));
    // String id = (user.data.id) as String;

    Map<String, String> body = {'user_id': userID.toString()};
    Map<String, String> headers = {'Authorization': 'Bearer $jwt'};
    print('send $body');
    List<Rewards> rewards = [];
    try {
      var response =
          await client.post(Uri.parse(url), body: body, headers: headers);
      // const jsonResponse = json.decode(response);

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);
      if (jsonResponse['success'] == false) {
        Get.to(() => const LoginScreen());
      } else {
        var parsedJSON = jsonResponse['data']['early'];

        rewards = rewardsFromJson(json.encode(parsedJSON));
      }

      return rewards;
      // jsonMap.map((e) => print('api:::$e'));
      // print(parsedJSON);
    } catch (e) {
      rethrow;
    }
  }

  static Future<WishlistModel?> getWishlist(params) async {
    String url = '${baseURL}api/v1/ecomm/getWishlist';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    WishlistModel? wishlishModel;
    try {
      var response = await client.post(Uri.parse(url),
          body: json.encode(params), headers: headers);

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);

      if (jsonResponse['success'] == false) {
        Get.to(() => const RootPage());
      } else {
        wishlishModel = WishlistModel.fromJson(jsonResponse);
        print(wishlishModel.toJson());
        return wishlishModel;
      }
    } catch (e, stackTrace) {
      print('Error in $url =>:: $e  \nStackTrace: $stackTrace');
      return wishlishModel;
    }
    return wishlishModel;
  }

  //register User
  static Future registerUsr(params) async {
    if (kDebugMode) {
      print(params);
    }
    String url = '${baseURL}api/v1/mobile/registerUser';

    // print('body1$requestBody');

    UserData? userModel;
    try {
      final response = await client.post(
        Uri.parse(url),
        body: params,
      );
      print(response.statusCode);
      // const jsonResponse = json.decode(response);
      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final jsonMap = json.decode(jsonRes);
        print('here-$jsonMap');
        // final storage = new FlutterSecureStorage();
        // await storage.write(key: "jwt", value: jsonMap['data']['token']);

        // String? jwt = await storage.read(key: "jwt");
        // print(jwt);
        // print(jsonMap['data']['token']);
        // userModel = User.fromJson(jsonMap);
        userModel = UserData.fromJson(jsonMap);
      }
      return userModel;
    } catch (e) {
      print(e);
    }
    return userModel;
  }

  //GoogleSigin

  static Future onGoogleSignin(params) async {
    if (kDebugMode) {
      print(params);
    }
    String url = '${baseURL}api/v1/mobile/loginWithGoogleIdToken';

    // print('body1$requestBody');

    UserData? userModel;
    try {
      final response = await client.post(
        Uri.parse(url),
        body: params,
      );
      print(response.statusCode);
      // const jsonResponse = json.decode(response);
      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final jsonMap = json.decode(jsonRes);
        print('here-$jsonMap');
        // final storage = new FlutterSecureStorage();
        // await storage.write(key: "jwt", value: jsonMap['data']['token']);

        // String? jwt = await storage.read(key: "jwt");
        // print(jwt);
        // print(jsonMap['data']['token']);
        // userModel = User.fromJson(jsonMap);
        userModel = UserData.fromJson(jsonMap);
      }
      return userModel;
    } catch (e) {
      print(e);
    }
    return userModel;
  }

  static Future verifyOtp(params) async {
    if (kDebugMode) {
      print(params);
    }
    String url = '${baseURL}api/v1/mobile/verifyOtp';

    // print('body1$requestBody');

    UserData? userModel;
    try {
      final response = await client.post(Uri.parse(url), body: params);
      if (kDebugMode) {
        print(response.statusCode);
      }
      // const jsonResponse = json.decode(response);
      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final jsonMap = json.decode(jsonRes);
        if (kDebugMode) {
          print('here-$jsonMap');
        }

        // final storage = new FlutterSecureStorage();
        // await storage.write(key: "jwt", value: jsonMap['data']['token']);

        // String? jwt = await storage.read(key: "jwt");
        // print(jwt);
        // print(jsonMap['data']['token']);
        // userModel = User.fromJson(jsonMap);
        userModel = UserData.fromJson(jsonMap);
      }
      if (response.statusCode == 403) {
        var jsonRes = response.body;
        var jsonMap = json.decode(jsonRes);

        // storage.write('mobile', jsonMap['data']['mobile'].toString());
        // storage.write('jwt', jsonMap['data']['token']);

        userModel = UserData.fromJson(jsonMap);
      }

      return userModel;
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
    return userModel;
  }

  static Future<ImageUploadModel?> uploadProfileImage(String filePath) async {
    // var docData = UploadPassportModel();
    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';
    final UserController userController = Get.find();
    String url = '${baseURL}api/v1/mobile/uploadUserImage';
    if (kDebugMode) {
      print("url ::=> $url");
    }
    var imageRequest = http.MultipartRequest('POST', Uri.parse(url));
    imageRequest.headers.putIfAbsent('Authorization', () => 'Bearer $jwt');
    imageRequest.files
        .add(await http.MultipartFile.fromPath('user_img', filePath));
    imageRequest.fields['user_id'] =
        userController.userdata.value.data!.id.toString();

    // print(imageRequest.fields);
    try {
      var streamedResponse = await imageRequest.send();
      var response = await http.Response.fromStream(streamedResponse);
      if (kDebugMode) {
        print("response  ::=>${response.statusCode} ${response.body}");
      }
      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final data = imageUploadModelFromJson(response.body);
        final jsonMap = json.decode(jsonRes);
        print('$jsonMap');
        if (data != null) {
          if (data.success == true) {
            // final data = jsonMap['data'];
            return data;
          }
          return null;
        }
      }
    } catch (e) {
      customToast(message: "error:couldn't upload image");
      print(e.toString());
      return null;
      // return null;
    }
    return null;
  }

  static Future<UserData?> getUserDetails(params) async {
    String url = '${baseURL}api/v1/mobile/getUserDetails';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    UserData? userModel;
    try {
      var response = await client.post(Uri.parse(url),
          body: json.encode(params), headers: headers);

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);

      if (jsonResponse['success'] == false) {
        Get.to(() => const LoginScreen());
      } else {
        // var parsedJSON = jsonResponse['data'];
        // print('here--- ${jsonResponse['data']}');
        userModel = UserData.fromJson(jsonResponse);
        print(userModel.toJson());
        return userModel;
      }
    } catch (e, stackTrace) {
      print('Error in $url =>:: $e  \nStackTrace: $stackTrace');
      return userModel;
    }
    return null;
  }

  static Future sendOTP(params) async {
    if (kDebugMode) {
      print(params);
    }
    String url = '${baseURL}api/v1/mobile/sendOtp';

    // print('body1$requestBody');

    UserData? userModel;
    try {
      final response = await client.post(Uri.parse(url), body: params);
      if (kDebugMode) {
        print(response.statusCode);
      }
      // const jsonResponse = json.decode(response);
      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final jsonMap = json.decode(jsonRes);
        if (kDebugMode) {
          print('here-$jsonMap');
        }
        // final storage = new FlutterSecureStorage();
        // await storage.write(key: "jwt", value: jsonMap['data']['token']);

        // String? jwt = await storage.read(key: "jwt");
        // print(jwt);
        // print(jsonMap['data']['token']);
        // userModel = User.fromJson(jsonMap);

        userModel = UserData.fromJson(jsonMap);
      }

      return userModel;
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
    return userModel;
  }
}
// To parse this JSON data, do
//
//     final imageUploadModel = imageUploadModelFromJson(jsonString);

ImageUploadModel? imageUploadModelFromJson(String str) =>
    ImageUploadModel.fromJson(json.decode(str));

String imageUploadModelToJson(ImageUploadModel? data) =>
    json.encode(data!.toJson());

class ImageUploadModel {
  ImageUploadModel({
    this.success,
    this.msg,
    this.data,
  });

  bool? success;
  String? msg;
  Data? data;

  factory ImageUploadModel.fromJson(Map<String, dynamic> json) =>
      ImageUploadModel(
        success: json["success"],
        msg: json["msg"],
        data: json["data"],
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "msg": msg,
        "data": data,
      };
}

class Data {
  Data({
    this.imageId,
    this.url,
  });

  int? imageId;
  String? url;

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        imageId: json["image_id"],
        url: json["url"],
      );

  Map<String, dynamic> toJson() => {
        "image_id": imageId,
        "url": url,
      };
}
