import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:Rapsap/model/user_model/usermodel.dart';
import 'package:Rapsap/view/widgets/commons.dart';
import 'package:http/http.dart' as http;
import 'package:Rapsap/model/address_model/address_model/address_model.dart';
import 'package:Rapsap/view/screens/root_page/root_page.dart';

import '../model/rewards/rewardsmode.dart';

class AccountService {
  static final UserController userController = Get.find<UserController>();
  static var client = http.Client();

  static Future createAddress(params) async {
    String url = baseURL + 'api/v1/mobile/createAddress';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';
    print(jwt);

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    try {
      var response = await http.post(Uri.parse(url),
          body: json.encode(params), headers: headers);

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);

      if (jsonResponse['success'] == false) {
        Get.to(() => RootPage());
      } else {
        // orderList = addressFromJson(json.encode(jsonResponse['data']));
        return jsonResponse;
      }
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('Error in $url =>:: $e  \nStackTrace: $stackTrace');
      }
      return 0;
    }
  }

  static Future deleteAddress(params) async {
    String url = baseURL + 'api/v1/mobile/deleteAddress';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';
    print(jwt);

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    try {
      var response = await http.post(Uri.parse(url),
          body: json.encode(params), headers: headers);

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);

      if (jsonResponse['success'] == false) {
        Get.to(() => RootPage());
      } else {
        print(jsonResponse);
        // orderList = addressFromJson(json.encode(jsonResponse['data']));
        return jsonResponse;
      }
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('Error in $url =>:: $e  \nStackTrace: $stackTrace');
      }
      return 0;
    }
  }

  static Future checkDailyRewards() async {
    // SharedPreferences share = await SharedPreferences.getInstance();
    // String jwt = (share.getString('jwt') ?? '');
    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';
    String url = baseURL + 'api/v1/mobile/getDailyOfferByUserID';
    Map<String, String> headers = {'Authorization': 'Bearer $jwt'};

    Map<String, String> body = {
      'user_id': userController.userdata.value.data!.id!.toString()
    };

    var currentReward;
    try {
      final response =
          await client.post(Uri.parse(url), body: body, headers: headers);
      // const jsonResponse = json.decode(response);
      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final jsonMap = json.decode(jsonRes);
        // print('here-$jsonMap');
        currentReward = jsonMap;
      }
      return currentReward;
    } catch (e) {
      print(e);
    }
    return currentReward;
  }

  static Future<List<Rewards>> getUserRewards1() async {
    String url = baseURL + 'api/v1/mobile/getUserRewards';
    // SharedPreferences share = await SharedPreferences.getInstance();
    // String jwt = (share.getString('jwt') ?? '');
    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';
    // User user;
    // print(userToJson(user));
    // String id = (user.data.id) as String;coupon

    Map<String, String> body = {
      'user_id': userController.userdata.value.data!.id!.toString()
    };
    Map<String, String> headers = {'Authorization': 'Bearer $jwt'};
    print('send $body');
    List<Rewards> rewards = [];
    try {
      var response =
          await client.post(Uri.parse(url), body: body, headers: headers);
      // const jsonResponse = json.decode(response);

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);
      if (jsonResponse['success'] == false) {
        Get.to(() => RootPage());
      } else {
        var parsedJSON = jsonResponse['data']['early'];

        rewards = rewardsFromJson(json.encode(parsedJSON));
      }

      return rewards;
      // jsonMap.map((e) => print('api:::$e'));
      // print(parsedJSON);

    } catch (e) {
      rethrow;
    }
  }

  static Future updateAddress(params) async {
    String url = baseURL + 'api/v1/mobile/updateAddress';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';
    print(jwt);

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    try {
      var response = await http.post(Uri.parse(url),
          body: json.encode(params), headers: headers);

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);

      if (jsonResponse['success'] == false) {
        print(jsonResponse['msg']);
        Get.to(() => RootPage());
      } else {
        // orderList = addressFromJson(json.encode(jsonResponse['data']));
        return jsonResponse;
      }
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('Error in $url =>:: $e  \nStackTrace: $stackTrace');
      }
      return 0;
    }
  }

  static Future<AddressModel?> getAddress(params) async {
    String url = baseURL + 'api/v1/mobile/getAddress';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';
    print(jwt);

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };
    var result;
    try {
      var response = await http.post(Uri.parse(url),
          body: json.encode(params), headers: headers);
      print(response.body);

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);

      if (jsonResponse['success'] == false) {
      } else {
        // orderList = addressFromJson(json.encode(jsonResponse['data']));
        return AddressModel.fromJson(response.body);
      }
    } catch (e, stackTrace) {
      print('Error in $url =>:: $e  \nStackTrace: $stackTrace');
    }
  }
}
