import 'dart:developer';

import 'package:Rapsap/main.dart';
import 'package:Rapsap/model/searchModel/search_model/search_model.dart';

import '../view/widgets/commons.dart';
import 'package:http/http.dart' as http;

class SearchService {
  static Future<SearchModel?> getProducts(query, page) async {
    String url = baseURL + 'api/v1/ecomm/getProducts';

    Map<String, dynamic> body = {
      "key": "JWT",
      "secret": "RAPSAP",
      "page": page.toString(),
      "keyword": query,
      "type": "user",
      "store_id": (storage.read("storeID") ?? 0).toString()
    };
    Map<String, String> headers = {};

    try {
      final response = await http.post(
        Uri.parse(url),
        body: body,
        headers: headers,
      );

      log("status:::${response.statusCode}");

      if (response.statusCode == 200) {
        final responseModel = SearchModel.fromJson(response.body);
        return responseModel;
      }
      return null;
    } catch (e) {
      log("$e");
    }
    return null;
  }
}
