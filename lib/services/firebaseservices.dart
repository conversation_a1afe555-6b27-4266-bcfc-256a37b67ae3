import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';

import '../view/widgets/commons.dart';
import '../view/widgets/notification_service.dart';

class FirebaseService {
  static final FirebaseRemoteConfig remoteConfig =
      FirebaseRemoteConfig.instance;
  static final FirebaseDynamicLinks dynamicLinks =
      FirebaseDynamicLinks.instance;

  static final FirebaseAnalytics firebaseAnalytics = FirebaseAnalytics.instance;

  static List<NavigatorObserver> analyticsObserver = [
    FirebaseAnalyticsObserver(analytics: FirebaseService.firebaseAnalytics),
  ];

  static init() async {
    await Firebase.initializeApp();
    await NotificationService().init();
    remoteconfig();

    crashlytics();
  }

  static crashlytics() {
    if (!kDebugMode) {
      FlutterError.onError = (errorDetails) {
        FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
      };
      // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
      PlatformDispatcher.instance.onError = (error, stack) {
        FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
        return true;
      };
    }
  }

  static remoteconfig() async {
    await remoteConfig.setConfigSettings(RemoteConfigSettings(
      fetchTimeout: const Duration(seconds: 10),
      minimumFetchInterval: const Duration(hours: 1),
    ));
  }
}
