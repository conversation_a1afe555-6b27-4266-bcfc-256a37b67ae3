import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:Rapsap/model/order/orderdetailmodel/order_detail_model/order_detail_model.dart';
import 'package:Rapsap/view/widgets/commons.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../main.dart';
import 'package:http/http.dart' as http;

import '../model/configmodel.dart';
import '../model/couponmodel.dart';
import '../model/order/order_list_model/order_list_model/order_list_model.dart';

class OrderServices {
  static final UserController userController = Get.find<UserController>();
  static var client = http.Client();

  static Future initiatePayment(params) async {
    String url = '${baseURL}api/v1/ecomm/initiatePayment';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };
    // ignore: prefer_typing_uninitialized_variables
    var result;
    try {
      var response = await client.post(Uri.parse(url),
          body: json.encode(params), headers: headers);

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);
      // print('here--- ${jsonResponse['data']}');
      result = jsonResponse;
      return result;
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('Error in $url =>:: $e  \nStackTrace: $stackTrace');
      }
      return result;
    }
  }

  static Future cancelOrder(params) async {
    String url = '${baseURL}api/v1/ecomm/cancelOrder';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    print(json.encode(params));
    try {
      var response = await client.post(Uri.parse(url),
          body: json.encode(params), headers: headers);

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);

      if (jsonResponse['success'] == false) {
        Get.to(() => const LoginScreen());
      } else {
        // orderList = addressFromJson(json.encode(jsonResponse['data']));
        return jsonResponse;
      }
    } catch (e, stackTrace) {
      print('Error in $url =>:: $e  \nStackTrace: $stackTrace');
      return 0;
    }
  }

  static Future<List<Coupon>> getOffer(params) async {
    String url = '${baseURL}api/v1/ecomm/getOffer';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';
    // UserController usertCTRL = Get.find();

    // Map<String, String> body = {
    //   'keyword': params['keyword'].toString(),
    //   'size': params['size'].toString(),
    //   'page': params['page'].toString(),
    // };
    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };
    // print('send $body');

    List<Coupon> offerlist = [];

    try {
      var response = await client.post(Uri.parse(url),
          body: json.encode(params), headers: headers);

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);

      if (jsonResponse['success'] == false) {
        Get.to(() => const LoginScreen());
      } else {
        // var parsedJSON = jsonResponse['data'];
        print('here--- ${jsonResponse['data']}');

        offerlist = couponFromJson(json.encode(jsonResponse['data']));
      }
      return offerlist;
    } catch (e, stackTrace) {
      print('Error in $url =>:: $e  \nStackTrace: $stackTrace');
      return offerlist;
    }
  }

  static notifyLaunching(params) async {
    String url = '${baseURL}api/v1/mobile/saveDetailsForNotification';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };
    print(params);

    try {
      var response = await client.post(Uri.parse(url),
          body: json.encode(params), headers: headers);

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);
      print(jsonResponse);
      if (jsonResponse['success'] == false) {
        Get.to(() => const LoginScreen());
      } else {
        // var parsedJSON = jsonResponse['data'];
        // print('here--- ${jsonResponse['data']}');
        Fluttertoast.showToast(msg: 'You will notified when we are launching');

        return null;
      }
    } catch (e, stackTrace) {
      print('Error in $url =>:: $e  \nStackTrace: $stackTrace');
      return null;
    }
  }

  static Future<ConfigModel?> getConfig() async {
    String url = '${baseURL}api/v1/ecomm/getConfig';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    ConfigModel? configModel;
    try {
      var response = await client.get(Uri.parse(url), headers: headers);

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);

      if (jsonResponse['success'] == false) {
        Get.to(() => const LoginScreen());
      } else {
        // var parsedJSON = jsonResponse['data'];
        // print('here--- ${jsonResponse['data']}');

        configModel = ConfigModel.fromJson(jsonResponse['data']);
        // if (configModel.isavailable == 0) {
        //   return Get.offAll(() => LaunchingSoon());
        // }

        return configModel;
      }
    } catch (e, stackTrace) {
      print('Error in $url =>:: $e  \nStackTrace: $stackTrace');
      return configModel;
    }
    return null;
  }

  static Future createOrder(params) async {
    String url = '${baseURL}api/v1/ecomm/createOrder';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };
    // ignore: prefer_typing_uninitialized_variables
    var result;
    try {
      var response = await client.post(Uri.parse(url),
          body: json.encode(params), headers: headers);

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);
      result = jsonResponse;
      return result;
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('Error in $url =>:: $e  \nStackTrace: $stackTrace');
      }
      return result;
    }
  }

  static Future updateOrder(params) async {
    String url = '${baseURL}api/v1/ecomm/updateOrder';

    String jwt = storage.read('JWT') ?? '';
    if (kDebugMode) {
      print(jwt);
    }

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };
    // ignore: prefer_typing_uninitialized_variables
    var result;
    try {
      var response = await client.post(Uri.parse(url),
          body: json.encode(params), headers: headers);

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);
      result = jsonResponse;
      return result;
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('Error in $url =>:: $e  \nStackTrace: $stackTrace');
      }
      return result;
    }
  }

  static Future<OrderListModel?> getOrder() async {
    String url = '${baseURL}api/v1/ecomm/getOrders';

    String jwt = storage.read('JWT') ?? '';
    if (kDebugMode) {
      print(jwt);
    }
    Map<String, dynamic> payload = {
      "user_id": userController.userdata.value.data!.id,
      "type": "user",
    };

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };
    // ignore: prefer_typing_uninitialized_variables
    var result;
    try {
      var response = await client.post(Uri.parse(url),
          body: json.encode(payload), headers: headers);
      print(response.statusCode);
      if (response.statusCode == 200) {
        var jsonRes = response.body;
        var jsonResponse = json.decode(jsonRes);
        result = jsonResponse;
        return OrderListModel.fromJson(response.body);
      } else {
        print(response);
      }
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('Error in $url =>:: $e  \nStackTrace: $stackTrace');
      }
      return OrderListModel.fromJson(result);
    }
    return OrderListModel.fromJson(result);
  }

  static Future<OrderDetailModel?> getOrderById(orderid) async {
    String url = '${baseURL}api/v1/ecomm/getOrderByID';

    String jwt = storage.read('JWT') ?? '';
    if (kDebugMode) {
      print(jwt);
    }
    Map<String, dynamic> payload = {
      "order_id": orderid,
      "type": "user",
    };

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };
    // ignore: prefer_typing_uninitialized_variables
    var result;
    try {
      var response = await client.post(Uri.parse(url),
          body: json.encode(payload), headers: headers);
      print(response.statusCode);
      if (response.statusCode == 200) {
        var jsonRes = response.body;
        var jsonResponse = json.decode(jsonRes);
        result = jsonResponse;
        return OrderDetailModel.fromJson(response.body);
      } else {
        print(response);
      }
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('Error in $url =>:: $e  \nStackTrace: $stackTrace');
      }
      return OrderDetailModel.fromJson(result);
    }
    return OrderDetailModel.fromJson(result);
  }

  static Future vaidateCouponCode(params) async {
    String url = '${baseURL}api/v1/ecomm/validateDiscount';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';

    Map<String, String> body = {
      'coupon_code': params['coupon_code'].toString(),
      'sub_total': params['sub_total'].toString(),
    };
    Map<String, String> headers = {'Authorization': 'Bearer $jwt'};
    print('send $body');

    var coupon;

    try {
      var response =
          await client.post(Uri.parse(url), body: body, headers: headers);

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);

      coupon = jsonResponse;

      return coupon;
    } catch (e, stackTrace) {
      print('Error in $url =>:: $e  \nStackTrace: $stackTrace');
      return coupon;
    }
  }
}
