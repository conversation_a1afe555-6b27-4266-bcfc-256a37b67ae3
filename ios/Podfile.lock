PODS:
  - AppAuth (1.6.2):
    - AppAuth/Core (= 1.6.2)
    - AppAuth/ExternalUserAgent (= 1.6.2)
  - AppAuth/Core (1.6.2)
  - AppAuth/ExternalUserAgent (1.6.2):
    - AppAuth/Core
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - Flutter
  - Firebase/Analytics (10.9.0):
    - Firebase/Core
  - Firebase/Auth (10.9.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 10.9.0)
  - Firebase/Core (10.9.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.9.0)
  - Firebase/CoreOnly (10.9.0):
    - FirebaseCore (= 10.9.0)
  - Firebase/Crashlytics (10.9.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 10.9.0)
  - Firebase/DynamicLinks (10.9.0):
    - Firebase/CoreOnly
    - FirebaseDynamicLinks (~> 10.9.0)
  - Firebase/InAppMessaging (10.9.0):
    - Firebase/CoreOnly
    - FirebaseInAppMessaging (~> 10.9.0-beta)
  - Firebase/Messaging (10.9.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.9.0)
  - Firebase/Performance (10.9.0):
    - Firebase/CoreOnly
    - FirebasePerformance (~> 10.9.0)
  - Firebase/RemoteConfig (10.9.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 10.9.0)
  - firebase_analytics (10.4.2):
    - Firebase/Analytics (= 10.9.0)
    - firebase_core
    - Flutter
  - firebase_auth (4.6.2):
    - Firebase/Auth (= 10.9.0)
    - firebase_core
    - Flutter
  - firebase_core (2.13.1):
    - Firebase/CoreOnly (= 10.9.0)
    - Flutter
  - firebase_crashlytics (3.3.2):
    - Firebase/Crashlytics (= 10.9.0)
    - firebase_core
    - Flutter
  - firebase_dynamic_links (5.3.2):
    - Firebase/DynamicLinks (= 10.9.0)
    - firebase_core
    - Flutter
  - firebase_in_app_messaging (0.7.3-2):
    - Firebase/InAppMessaging (= 10.9.0)
    - firebase_core
    - Flutter
  - firebase_messaging (14.6.2):
    - Firebase/Messaging (= 10.9.0)
    - firebase_core
    - Flutter
  - firebase_performance (0.9.2-2):
    - Firebase/Performance (= 10.9.0)
    - firebase_core
    - Flutter
  - firebase_remote_config (4.2.2):
    - Firebase/RemoteConfig (= 10.9.0)
    - firebase_core
    - Flutter
  - FirebaseABTesting (10.10.0):
    - FirebaseCore (~> 10.0)
  - FirebaseAnalytics (10.9.0):
    - FirebaseAnalytics/AdIdSupport (= 10.9.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.9.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.9.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAppCheckInterop (10.10.0)
  - FirebaseAuth (10.9.0):
    - FirebaseAppCheckInterop (~> 10.0)
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
  - FirebaseCore (10.9.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreExtension (10.10.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.10.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.9.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseDynamicLinks (10.9.0):
    - FirebaseCore (~> 10.0)
  - FirebaseInAppMessaging (10.9.0-beta):
    - FirebaseABTesting (~> 10.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseInstallations (10.10.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.9.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebasePerformance (10.9.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfig (~> 10.0)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/ISASwizzler (~> 7.8)
    - GoogleUtilities/MethodSwizzler (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseRemoteConfig (10.9.0):
    - FirebaseABTesting (~> 10.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseSessions (10.10.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.10)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - Flutter (1.0.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - geocoding_ios (1.0.5):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - GoogleMaps (< 8.0)
  - google_sign_in_ios (0.0.1):
    - Flutter
    - GoogleSignIn (~> 6.2)
  - GoogleAppMeasurement (10.9.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.9.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.9.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.9.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.9.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.2.3):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (5.2.0):
    - GoogleMaps/Maps (= 5.2.0)
  - GoogleMaps/Base (5.2.0)
  - GoogleMaps/Maps (5.2.0):
    - GoogleMaps/Base
  - GoogleSignIn (6.2.4):
    - AppAuth (~> 1.5)
    - GTMAppAuth (~> 1.3)
    - GTMSessionFetcher/Core (< 3.0, >= 1.1)
  - GoogleUtilities/AppDelegateSwizzler (7.11.1):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.11.1):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.11.1)
  - GoogleUtilities/Logger (7.11.1):
    - GoogleUtilities/Environment
  - GoogleUtilities/MethodSwizzler (7.11.1):
    - GoogleUtilities/Logger
  - GoogleUtilities/Network (7.11.1):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.11.1)"
  - GoogleUtilities/Reachability (7.11.1):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.11.1):
    - GoogleUtilities/Logger
  - GTMAppAuth (1.3.1):
    - AppAuth/Core (~> 1.6)
    - GTMSessionFetcher/Core (< 3.0, >= 1.5)
  - GTMSessionFetcher/Core (2.3.0)
  - image_picker_ios (0.0.1):
    - Flutter
  - nanopb (2.30909.0):
    - nanopb/decode (= 2.30909.0)
    - nanopb/encode (= 2.30909.0)
  - nanopb/decode (2.30909.0)
  - nanopb/encode (2.30909.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.2.0)
  - PromisesSwift (2.2.0):
    - PromisesObjC (= 2.2.0)
  - razorpay-pod (1.3.2)
  - razorpay_flutter (1.1.10):
    - Flutter
    - razorpay-pod
  - ReachabilitySwift (5.0.0)
  - rive_common (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - smart_auth (0.0.1):
    - Flutter
  - sqflite (0.0.3):
    - Flutter
    - FMDB (>= 2.7.5)
  - Toast (4.0.0)
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_dynamic_links (from `.symlinks/plugins/firebase_dynamic_links/ios`)
  - firebase_in_app_messaging (from `.symlinks/plugins/firebase_in_app_messaging/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_performance (from `.symlinks/plugins/firebase_performance/ios`)
  - firebase_remote_config (from `.symlinks/plugins/firebase_remote_config/ios`)
  - Flutter (from `Flutter`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - geocoding_ios (from `.symlinks/plugins/geocoding_ios/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - razorpay_flutter (from `.symlinks/plugins/razorpay_flutter/ios`)
  - rive_common (from `.symlinks/plugins/rive_common/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - smart_auth (from `.symlinks/plugins/smart_auth/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - AppAuth
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseDynamicLinks
    - FirebaseInAppMessaging
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebasePerformance
    - FirebaseRemoteConfig
    - FirebaseSessions
    - FMDB
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - razorpay-pod
    - ReachabilitySwift
    - Toast

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_dynamic_links:
    :path: ".symlinks/plugins/firebase_dynamic_links/ios"
  firebase_in_app_messaging:
    :path: ".symlinks/plugins/firebase_in_app_messaging/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_performance:
    :path: ".symlinks/plugins/firebase_performance/ios"
  firebase_remote_config:
    :path: ".symlinks/plugins/firebase_remote_config/ios"
  Flutter:
    :path: Flutter
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  geocoding_ios:
    :path: ".symlinks/plugins/geocoding_ios/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  razorpay_flutter:
    :path: ".symlinks/plugins/razorpay_flutter/ios"
  rive_common:
    :path: ".symlinks/plugins/rive_common/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  smart_auth:
    :path: ".symlinks/plugins/smart_auth/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  AppAuth: 3bb1d1cd9340bd09f5ed189fb00b1cc28e1e8570
  connectivity_plus: 07c49e96d7fc92bc9920617b83238c4d178b446a
  device_info_plus: 7545d84d8d1b896cb16a4ff98c19f07ec4b298ea
  Firebase: bd152f0f3d278c4060c5c71359db08ebcfd5a3e2
  firebase_analytics: 48ed4a230abf9f585f7e7b477791effe96f5a791
  firebase_auth: 3b4b3d296a504a24c0f518ec1a2dcc43e7b6ed81
  firebase_core: ce64b0941c6d87c6ef5022ae9116a158236c8c94
  firebase_crashlytics: 9b80d1944507cc07fa1c4455797f7d2eb7c8873f
  firebase_dynamic_links: db9f2ebcc3ea646e76a1d3ee37e9e57890ff0a83
  firebase_in_app_messaging: 5a30c5db1400056b7beb869885ea1de5daf4d360
  firebase_messaging: 42912365e62efc1ea3e00724e5eecba6068ddb88
  firebase_performance: d11d1fd9591547f6b75f325aaadd6550eaf7e090
  firebase_remote_config: a4d88ffa663b3ccd09115c90680b813203cb9184
  FirebaseABTesting: b2a87808d90f02766fcffb3957d3e397e7accc15
  FirebaseAnalytics: 5ea0db4893825e7b0149d575352cd838236313dc
  FirebaseAppCheckInterop: 7d3521f56872cf74a01792c0a095a30e054ff6ae
  FirebaseAuth: 21d5e902fcea44d0d961540fc4742966ae6118cc
  FirebaseCore: b68d3616526ec02e4d155166bbafb8eca64af557
  FirebaseCoreExtension: 8d93ebbf6838a874b4ed82a564c9d6705f8365dd
  FirebaseCoreInternal: 971029061d326000d65bfdc21f5502c75c8b0893
  FirebaseCrashlytics: b60329455285aff853e54139d8ddbfe1e5f2b9f9
  FirebaseDynamicLinks: 8cb66c4f403aa6ddf86ff3bc3c383a652f344ce9
  FirebaseInAppMessaging: 652fa7b735d2bab457bb57f339270b679b2bb50c
  FirebaseInstallations: 52153982b057d3afcb4e1fbb3eb0b6d00611e681
  FirebaseMessaging: 6b7052cc3da7bc8e5f72bef871243e8f04a14eed
  FirebasePerformance: eee2f5da94fd7e5d15487649f8fe10a90c87c174
  FirebaseRemoteConfig: 5ea5834e8c518f377bf1af2d97ebd611914ebf2d
  FirebaseSessions: 5f9e62cd4096e24d2011cbd845b0efceffaee1ec
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_local_notifications: 0c0b1ae97e741e1521e4c1629a459d04b9aec743
  fluttertoast: fafc4fa4d01a6a9e4f772ecd190ffa525e9e2d9c
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  geocoding_ios: a389ea40f6f548de6e63006a2e31bf66ff80769a
  geolocator_apple: cc556e6844d508c95df1e87e3ea6fa4e58c50401
  google_maps_flutter_ios: abdac20d6ce8931f6ebc5f46616df241bfaa2cfd
  google_sign_in_ios: 1256ff9d941db546373826966720b0c24804bcdd
  GoogleAppMeasurement: 373bcbead1bb6a85be7a64d5d8f96284b762ea9c
  GoogleDataTransport: f0308f5905a745f94fb91fea9c6cbaf3831cb1bd
  GoogleMaps: 025272d5876d3b32604e5c080dc25eaf68764693
  GoogleSignIn: 5651ce3a61e56ca864160e79b484cd9ed3f49b7a
  GoogleUtilities: 9aa0ad5a7bc171f8bae016300bfcfa3fb8425749
  GTMAppAuth: 0ff230db599948a9ad7470ca667337803b3fc4dd
  GTMSessionFetcher: 3a63d75eecd6aa32c2fc79f578064e1214dfdec2
  image_picker_ios: 4a8aadfbb6dc30ad5141a2ce3832af9214a705b5
  nanopb: b552cce312b6c8484180ef47159bc0f65a1f0431
  package_info_plus: fd030dabf36271f146f1f3beacd48f564b0f17f7
  path_provider_foundation: eaf5b3e458fc0e5fbb9940fb09980e853fe058b8
  PromisesObjC: 09985d6d70fbe7878040aa746d78236e6946d2ef
  PromisesSwift: cf9eb58666a43bbe007302226e510b16c1e10959
  razorpay-pod: b129f60de5f0d0952bebfb9480a10909d4fd6b6d
  razorpay_flutter: 84b3bfd206ae9c9c2a9ba585524a1b3d8102b6c1
  ReachabilitySwift: 985039c6f7b23a1da463388634119492ff86c825
  rive_common: 60ae7896ab40f9513974f36f015de33f70d2c5c5
  shared_preferences_foundation: e2dae3258e06f44cc55f49d42024fd8dd03c590c
  smart_auth: 4bedbc118723912d0e45a07e8ab34039c19e04f2
  sqflite: 31f7eba61e3074736dff8807a9b41581e4f7f15a
  Toast: 91b396c56ee72a5790816f40d3a94dd357abc196
  url_launcher_ios: 08a3dfac5fb39e8759aeb0abbd5d9480f30fc8b4

PODFILE CHECKSUM: 5b48dbef421fbf03a881803a0e1cd9baec76c859

COCOAPODS: 1.11.3
